import { jsx as _jsx } from "react/jsx-runtime";
import { useTheme } from '@mui/material';
import { fontFamily } from 'theme/typography';
import { useMemo } from 'react';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { PolarComponent, TooltipComponent, GraphicComponent } from 'echarts/components';
import ReactEchart from 'components/base/ReactEchart';
echarts.use([PolarComponent, TooltipComponent, GraphicComponent, BarChart, CanvasRenderer]);
const VisitorsChart = ({ chartRef, ...rest }) => {
    const theme = useTheme();
    const option = useMemo(() => ({
        polar: {
            radius: [80, '75%'],
        },
        angleAxis: {
            max: 100,
            startAngle: 180,
            axisLine: {
                show: false,
            },
            splitLine: {
                show: false,
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                show: false,
            },
        },
        radiusAxis: {
            show: false,
            type: 'category',
            data: ['Direct', 'Social', 'Organic'],
        },
        tooltip: {},
        series: [
            {
                type: 'bar',
                data: [
                    {
                        type: 'Direct',
                        value: 50,
                        itemStyle: {
                            color: theme.palette.secondary.main,
                        },
                    },
                    {
                        type: 'Social',
                        value: 60,
                        itemStyle: {
                            color: theme.palette.secondary.lighter,
                        },
                    },
                    {
                        type: 'Organic',
                        value: 80,
                        itemStyle: {
                            color: theme.palette.primary.main,
                        },
                    },
                ],
                coordinateSystem: 'polar',
                barCategoryGap: '35%',
                label: {
                    show: false,
                },
            },
        ],
        graphic: [
            {
                type: 'text',
                left: 'center',
                top: 'middle',
                style: {
                    text: '150k',
                    fill: theme.palette.text.primary,
                    fontSize: theme.typography.h3.fontSize,
                    fontFamily: fontFamily.workSans,
                    fontWeight: 500,
                    letterSpacing: 1,
                },
            },
        ],
    }), [theme]);
    return _jsx(ReactEchart, { ref: chartRef, echarts: echarts, option: option, ...rest });
};
export default VisitorsChart;
