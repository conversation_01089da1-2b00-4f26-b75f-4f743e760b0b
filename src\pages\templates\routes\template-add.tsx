import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { AccordionDetails, InputLabel } from '@mui/material';
import Paper from '@mui/material/Paper';
import FileInputWithPreview from 'components/file-input.tsx';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import Checkbox from '@mui/material/Checkbox';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import useTemplateForm from 'pages/templates/hooks/useTemplateForm.ts';
import useCategories from 'pages/category/hooks/useCategories.ts';
import { Editor } from '@tinymce/tinymce-react';
import { useRef } from 'react';
import { preOrderOptions, sellingOptions } from 'pages/templates/const/sellingOtions.ts';
import { regionsList } from 'pages/templates/const/regionList.ts';
import { languages } from 'pages/templates/const/languages.ts';
import { Editor as TinyMCEEditor } from 'tinymce';
import CircularProgress from '@mui/material/CircularProgress';
import { fontFamily } from 'theme/typography.ts';
import { editorProps } from 'constant/editor.ts';
import SingleFileInput from 'components/single-file-input.tsx';
import { productTypes } from 'pages/templates/const/productType.ts';

export default function TemplateAdd() {
  const editorRef = useRef<TinyMCEEditor | null>(null);

  const { categories, categoryLoading } = useCategories({});

  const { formik } = useTemplateForm({ templateDetails: undefined });

  const handleSelectChange = (id: string, value: SelectedValue | undefined) => {
    formik.setFieldValue(id, value);
  };

  const handleCheckboxChange = (language: string, isChecked: boolean) => {
    if (isChecked) formik.setFieldValue('languages', [...formik.values.languages, language]);
    else
      formik.setFieldValue(
        'languages',
        formik.values.languages.filter((lang) => lang !== language),
      );
  };

  const getSubCategoryOptions = () => {
    const defaultOption = { _id: null, name: 'None' };
    if (!categories) return [defaultOption];

    const selectedCategory = categories.find(
      (category) => category._id === formik.values.category._id,
    );
    const childrenOptions =
      selectedCategory?.children.map((category) => ({
        _id: category._id,
        name: category.categoryName.en,
      })) ?? [];
    return [...childrenOptions, defaultOption];
  };

  if (categoryLoading || !categories) {
    return <CircularProgress />;
  }

  return (
    <Paper elevation={3} sx={{ py: 4, width: { xs: '100%', md: '75%' } }}>
      <Typography variant="h5" fontWeight={600} sx={{ fontSize: { xs: '16px', sm: '18px' } }}>
        Add Template
      </Typography>
      <Stack onSubmit={formik.handleSubmit} component="form" direction="column" gap={2} mt={4}>
        {formik.isSubmitting ? (
          <CircularProgress />
        ) : (
          <SingleFileInput
            label="Cover Image"
            getFile={(file) => formik.setFieldValue('coverImage', file)}
          />
        )}
        {formik.errors.coverImage && (
          <Typography
            variant="h6"
            color="red"
            fontSize={12}
            fontWeight={400}
            fontFamily={fontFamily.workSans}
          >
            {formik.errors.coverImage}
          </Typography>
        )}
        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="templateName"
          >
            Template Name
          </InputLabel>
          <TextField
            id="templateName"
            type="text"
            variant="filled"
            placeholder="Template Name"
            autoComplete="description"
            fullWidth
            required
            error={!!formik.errors.templateName && formik.touched.templateName}
            helperText={
              formik.errors.templateName && formik.touched.templateName
                ? formik.errors.templateName
                : ''
            }
            {...formik.getFieldProps('templateName')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="serviceFee"
          >
            Service Fee
          </InputLabel>
          <TextField
            id="serviceFee"
            type="text"
            variant="filled"
            placeholder="Service Fee"
            autoComplete="description"
            fullWidth
            required
            error={!!formik.errors.serviceFee && formik.touched.serviceFee}
            helperText={
              formik.errors.serviceFee && formik.touched.serviceFee ? formik.errors.serviceFee : ''
            }
            {...formik.getFieldProps('serviceFee')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="templateName"
          >
            Template Slug
          </InputLabel>
          <TextField
            id="slug"
            type="text"
            variant="filled"
            placeholder="Template Slug"
            autoComplete="description"
            fullWidth
            {...formik.getFieldProps('slug')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="listingType"
          >
            Listing Type
          </InputLabel>
          <SelectMenu
            id="listingType"
            value={formik.values.listingType}
            handleChange={handleSelectChange}
            options={productTypes}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="category"
          >
            Category
          </InputLabel>
          <SelectMenu
            id="category"
            value={formik.values.category}
            handleChange={handleSelectChange}
            options={[
              { _id: null, name: '--Choose--' },
              ...categories.map((item) => ({ _id: item._id, name: item.categoryName.en })),
            ]}
          />
          {formik.errors.category && (
            <Typography
              variant="h6"
              color="red"
              fontSize={12}
              fontWeight={400}
              fontFamily={fontFamily.workSans}
            >
              {formik.errors.category._id}
            </Typography>
          )}
        </Box>

        {formik.values.category._id && (
          <Box>
            <InputLabel
              component="label"
              sx={{ fontSize: '12px', marginBottom: '20px' }}
              size="small"
              htmlFor="subcategory"
            >
              Sub Category
            </InputLabel>
            <SelectMenu
              id="subcategory"
              value={formik.values.subcategory}
              handleChange={handleSelectChange}
              options={getSubCategoryOptions()}
            />
          </Box>
        )}

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="region"
          >
            Region
          </InputLabel>
          <SelectMenu
            id="region"
            value={formik.values.region}
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...regionsList]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="genres"
          >
            Genres
          </InputLabel>
          <TextField
            id="genres"
            type="text"
            variant="filled"
            placeholder="Genres"
            autoComplete="genres"
            fullWidth
            {...formik.getFieldProps('genres')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="releaseDate"
          >
            Release Date
          </InputLabel>
          <TextField
            id="releaseDate"
            type="date"
            variant="filled"
            placeholder="Template Slug"
            autoComplete="description"
            fullWidth
            {...formik.getFieldProps('releaseDate')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="preOrder"
          >
            Pre-Order
          </InputLabel>
          <SelectMenu
            id="preOrder"
            value={formik.values.preOrder}
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...preOrderOptions]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="price"
          >
            Price ($)
          </InputLabel>
          <TextField
            id="price"
            type="number"
            variant="filled"
            placeholder="Template price"
            autoComplete="price"
            fullWidth
            {...formik.getFieldProps('price')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="specificCountrySellingOption"
          >
            Specific Country Selling Option
          </InputLabel>
          <SelectMenu
            id="specificCountrySellingOption"
            value={formik.values.specificCountrySellingOption}
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...sellingOptions]}
          />
        </Box>

        <Box>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Checkbox
              inputProps={{ 'aria-label': 'dlc' }}
              onChange={(e) => formik.setFieldValue('dlc', e.target.checked)}
            />
            <InputLabel
              component="label"
              sx={{ fontSize: '12px', marginBottom: '20px' }}
              size="small"
              htmlFor="dlc"
            >
              DLC
            </InputLabel>
          </Stack>
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="Language"
          >
            Language
          </InputLabel>
          {languages.map((language) => (
            <Box display="flex" alignItems="center" gap="10px" key={language}>
              <Checkbox
                inputProps={{ 'aria-label': language }}
                onChange={(e) => handleCheckboxChange(language, e.target.checked)}
              />
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="language"
              >
                {language}
              </InputLabel>
            </Box>
          ))}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="videos"
          >
            Add Video
          </InputLabel>
          <TextField
            id="videos"
            type="text"
            variant="filled"
            placeholder="Template videos"
            autoComplete="videos"
            fullWidth
            {...formik.getFieldProps('videos')}
            onChange={(e) => formik.setFieldValue('videos', e.target.value)}
          />
          {formik.touched.videos && (
            <Typography
              variant="h6"
              color="red"
              fontSize={12}
              fontWeight={400}
              fontFamily={fontFamily.workSans}
            >
              {formik.errors.videos}
            </Typography>
          )}
        </Box>

        {formik.isSubmitting ? (
          <CircularProgress />
        ) : (
          <Box>
            <FileInputWithPreview
              label="Add Image"
              getFile={(file) => formik.setFieldValue('images', file)}
            />
          </Box>
        )}

        <Accordion sx={{ paddingX: 0 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography sx={{ paddingX: 0 }}>Details</Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ paddingX: 0 }}>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.title"
              >
                Title
              </InputLabel>
              <TextField
                id="details.title"
                type="text"
                variant="filled"
                placeholder="Title"
                autoComplete="details.title"
                fullWidth
                {...formik.getFieldProps('details.title')}
              />
            </Box>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.description"
              >
                Description
              </InputLabel>
              <Editor
                onInit={(_evt, editor) => (editorRef.current = editor)}
                onBlur={() =>
                  formik.setFieldValue('details.description', editorRef.current?.getContent())
                }
                initialValue=""
                {...editorProps}
              />
            </Box>
            <Stack direction="column" mt={4} gap={1}>
              <Typography variant="h5" fontWeight={600}>
                SEO
              </Typography>
              <TextField
                id="details.seo.metaTitle"
                type="text"
                variant="filled"
                placeholder="Title"
                autoComplete="details.seo.metaTitle"
                fullWidth
                {...formik.getFieldProps('details.seo.metaTitle')}
              />
              <TextField
                id="details.seo.metaDescription"
                type="text"
                variant="filled"
                placeholder="Description"
                autoComplete="details.seo.metaDescription"
                fullWidth
                {...formik.getFieldProps('details.seo.metaDescription')}
              />
              <TextField
                id="details.seo.metaKeywords"
                type="text"
                variant="filled"
                placeholder="Keywords"
                autoComplete="details.seo.metaKeywords"
                fullWidth
                {...formik.getFieldProps('details.seo.metaKeywords')}
              />
            </Stack>
          </AccordionDetails>
        </Accordion>

        <Button
          type="submit"
          variant="contained"
          size="medium"
          fullWidth
          disabled={!formik.isValid || formik.isSubmitting}
        >
          {formik.isSubmitting ? 'Processing...' : 'Submit'}
        </Button>
      </Stack>
    </Paper>
  );
}