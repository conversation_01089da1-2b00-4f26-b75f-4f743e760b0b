import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllTaxes } from 'pages/tax/api/getAllTaxes.ts';

export default function useTaxList() {
  const { data: taxList, loading: taxLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['tax'],
      queryFn: () => getAllTaxes(),
      onError: showError,
    }),
    transform: (response) => response.data,
  });
  return { taxList, taxLoading };
}
