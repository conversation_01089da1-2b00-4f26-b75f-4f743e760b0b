export interface OrderResponse {
    status: string;
    results: number;
    data: {
        orders: Order[];
    };
}

export interface Order {
    userDetails: UserDetails;
    _id: string;
    user: string;
    items: OrderItem[];
    subtotal: number;
    serviceFee: number;
    total: number;
    savings: number;
    status: string;
    latest: boolean;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

interface UserDetails {
    _id: string;
    name: string;
    role: string;
    avatar: string;
    email: string;
}

interface OrderItem {
    offer: Offer;
    quantity: number;
    price: number;
    _id: string;
}

interface Offer {
    _id: string;
    template: {
        _id: string;
        price: number;
    };
    seller: {
        _id: string;
        name: string;
    };
    customerPays: number;
    name: string;
}
