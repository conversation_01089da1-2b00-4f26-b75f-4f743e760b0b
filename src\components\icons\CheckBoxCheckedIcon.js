import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { SvgIcon } from '@mui/material';
const CheckBoxCheckedIcon = (props) => {
    return (_jsxs(SvgIcon, { ...props, viewBox: "0 0 13 13", fill: "none", children: [_jsx("rect", { x: "1.26875", y: "0.680859", width: "11.4", height: "11.4", rx: "1.7", fill: "", stroke: "", strokeWidth: "0.6" }), _jsx("g", { clipPath: "url(#clip0_738_4022)", children: _jsx("path", { d: "M4.77051 6.709L6.08253 8.02103L9.36259 4.74097", stroke: "white", strokeLinecap: "round", strokeLinejoin: "round" }) }), _jsx("defs", { children: _jsx("clipPath", { id: "clip0_738_4022", children: _jsx("rect", { width: "5.6", height: "5.6", fill: "white", transform: "translate(4.2666 3.58105)" }) }) })] }));
};
export default CheckBoxCheckedIcon;
