import { FormControl } from '@mui/material';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import React from 'react';

export interface SelectedValue {
  _id: string | null | boolean;
  name: string;
}

interface SelectMenuProps {
    id: string;
    value: SelectedValue;
    handleChange: (id: string, value?: SelectedValue) => void;
    options: SelectedValue[];
}

const SelectMenu: React.FC<SelectMenuProps> = ({ id, value, handleChange, options }) => {
    return (
        <FormControl fullWidth required>
            <Select
                id={id}
                value={value.name}
                onChange={(event) => handleChange(id, options.find(option => option.name === event.target.value)) }
            >
                {options.map((option, index) => (
                    <MenuItem key={index} value={option.name}>
                        {option.name}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    );
};

export default SelectMenu;
