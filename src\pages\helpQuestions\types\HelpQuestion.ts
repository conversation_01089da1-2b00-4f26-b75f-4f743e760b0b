type Question = {
    _id: string;
    title: string;
    content: string;
    isActive: boolean;
    createdAt: string;
};

type QuestionSubcategory = {
    _id: string;
    title: string;
    questions: Question[];
    isActive: boolean;
    createdAt: string;
};

export type QuestionCategory = {
    _id: string;
    title: string;
    subcategories: QuestionSubcategory[];
    isActive: boolean;
    createdAt: string;
    __v: number;
};