import {showError, showSuccess, useQueryFix} from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { updateProduct } from 'pages/products/api/updateProduct.ts';
import {UpdateProps} from "pages/products/routes/all-products.tsx";

export default function useUpdateProduct(props: UpdateProps) {
  const queryClient = useQueryClient();
  const { refetch:updateRefetch, loading:updateLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['update-product', { ...props }],
      queryFn: () => updateProduct(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['all-products']).finally(()=> showSuccess("Offer updated successfully")),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { updateRefetch, updateLoading };
}