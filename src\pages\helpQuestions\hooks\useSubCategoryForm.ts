import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { showError, showSuccess } from 'vbrae-utils';
import { useParams } from 'react-router-dom';
import { patchSubCategory } from 'pages/helpQuestions/api/patchSubCategory.ts';
import { postSubCategory } from 'pages/helpQuestions/api/postSubCategory.ts';

type QuestionFormPropsDto = {
  existingSubCategory: {
    isActive?: boolean;
    title?: string;
    _id: string;
  };
  handleClose: () => void;
};

export type ErrorType = {
    message: string;
};

export const editSchema = Yup.object().shape({
    isActive: Yup.boolean(),
    title: Yup.string(),
    content: Yup.string(),
});

export default function useSubCategoryForm({existingSubCategory, handleClose}: QuestionFormPropsDto) {

    const { id = "", subCategoryId = "" } = useParams();

    const [initialValues, setInitialState] = useState({
        isActive: false,
        title: "",
    });

    useEffect(() => {
        if (!existingSubCategory.title) return;

        setInitialState({
            isActive: existingSubCategory.isActive || false,
            title: existingSubCategory.title,
        });
    }, [existingSubCategory]);

    const { mutateAsync : updateASync } = useMutation(patchSubCategory, {
        onError: (error: ErrorType) => showError(error),
    });

    const { mutateAsync } = useMutation(postSubCategory, {
        onError: (error: ErrorType) => showError(error),
    });

    const queryClient = useQueryClient();


    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: editSchema,
        onSubmit: async (values, { setSubmitting }) => {
            setSubmitting(true)
            let response;
            if(!existingSubCategory?.title) response = await mutateAsync({ ...values, categoryId : existingSubCategory._id });
            else response = await updateASync({ ...values, categoryId : id, subcategoryId: subCategoryId});
            setSubmitting(false);
            if(response){
                queryClient.invalidateQueries("questions").finally();
                queryClient.invalidateQueries("sub-category-questions").finally(()=> showSuccess("Successfully updated question"));
                handleClose();
            }
        },
    });

    return { formik };
}
