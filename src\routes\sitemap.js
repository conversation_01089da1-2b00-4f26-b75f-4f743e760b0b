export const sideMenuTop = [
    {
        id: 'dashboard',
        subheader: 'Dashboard',
        icon: 'mingcute:home-1-fill',
        path: '/',
    },
];
export const categoryOptions = [
    {
        id: 'categories',
        subheader: 'Categories',
        icon: 'mingcute:folder-open-line',
        items: [
            {
                name: 'Categories',
                pathName: 'categories',
                path: '/category',
            },
            {
                name: 'Add Categories',
                pathName: 'add-categories',
                path: '/category/add',
            },
            {
                name: 'Bulk Category Upload',
                pathName: 'bulk-category-upload',
                path: '/category/upload',
            },
        ],
    },
    {
        id: 'templates',
        subheader: 'Templates',
        icon: 'mingcute:file-import-line',
        items: [
            {
                name: 'List',
                pathName: 'List',
                path: '/templates/list',
            },
            // {
            //     name: 'IGDB',
            //     pathName: 'IGDB',
            //     path: '/templates/igdb',
            // },
            {
                name: 'Add Manually',
                pathName: 'Add-Manually',
                path: '/templates/add',
            },
            {
                name: 'Request',
                pathName: 'Request',
                path: '/templates/request',
            },
        ],
    },
];
