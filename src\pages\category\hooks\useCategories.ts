import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getCategories } from 'pages/category/api/getCategories.ts';

export interface CategoryProps {
  sortBy?: string;
  order?: string;
}

export default function useCategories(props : CategoryProps) {
  const { data: categories, loading: categoryLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['categories', { ...props }],
      queryFn: () => getCategories(props),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { categories, categoryLoading };
}
