import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Toolbar from '@mui/material/Toolbar';
import ButtonBase from '@mui/material/ButtonBase';
import IconButton from '@mui/material/IconButton';
import IconifyIcon from 'components/base/IconifyIcon';
import Image from 'components/base/Image';
import LogoImg from 'assets/images/Logo.png';
import LanguageSelect from './LanguageSelect';
import ProfileMenu from './ProfileMenu';
const Topbar = ({ isClosing, mobileOpen, setMobileOpen }) => {
    const handleDrawerToggle = () => {
        if (!isClosing) {
            setMobileOpen(!mobileOpen);
        }
    };
    return (_jsxs(Stack, { alignItems: "center", justifyContent: "space-between", mb: { xs: 0, lg: 1 }, children: [_jsxs(Stack, { spacing: 2, alignItems: "center", children: [_jsx(Toolbar, { sx: { display: { xm: 'block', lg: 'none' } }, children: _jsx(IconButton, { size: "medium", edge: "start", color: "inherit", "aria-label": "menu", onClick: handleDrawerToggle, children: _jsx(IconifyIcon, { icon: "mingcute:menu-line" }) }) }), _jsx(ButtonBase, { component: Link, href: "/", disableRipple: true, sx: { display: { xm: 'block', lg: 'none' } }, children: _jsx(Image, { src: LogoImg, alt: "logo", height: 24, width: 24 }) })] }), _jsxs(Stack, { spacing: 1, alignItems: "center", children: [_jsx(LanguageSelect, {}), _jsx(Tooltip, { title: "Notifications", children: _jsx(IconButton, { size: "large", sx: { color: 'text.secondary' }, children: _jsx(IconifyIcon, { icon: "ion:notifications" }) }) }), _jsx(ProfileMenu, {})] })] }));
};
export default Topbar;
