import React, {useEffect, useId, useState} from 'react';
import { Box, Button, CircularProgress, IconButton, InputLabel } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

interface FileInputWithPreviewProps {
  getFile: (files: File) => void;
  label: string;
  existingFile?: string;
  maxFiles?: number;
}

const SingleFileInput = ({
                                  getFile,
                                  label,
                                  existingFile = '',
                                  maxFiles = 1,
                              }: FileInputWithPreviewProps) => {
    const [filePreviews, setFilePreviews] = useState<string>( existingFile );
    const [isLoading, setIsLoading] = useState(false);
    const id = useId();

    useEffect(() => setFilePreviews(existingFile), [existingFile]);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        setIsLoading(true);

        if (files && files.length > 0) {
            const file = files[0]; // Only process the first file

            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onloadend = () => {
                    const preview = reader.result as string;
                    setFilePreviews(preview);
                    getFile(file);
                    setIsLoading(false);
                };
                reader.readAsDataURL(file);
            } else {
                alert(`${file.name} is not a valid image file (JPG, PNG, JPEG).`);
                setIsLoading(false);
            }
        } else {
            alert('No files selected or invalid file type.');
            setIsLoading(false);
        }
    };

    const handleRemoveFile = () => {
        setFilePreviews("");
    };

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px', width: '100%' }}
            >
                {label}
            </InputLabel>

            <input
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                multiple={maxFiles > 1}
                onChange={handleFileChange}
                id={`${id}-file-input`}
            />

            <label htmlFor={`${id}-file-input`}>
                <IconButton
                    component="span"
                    sx={{
                        backgroundColor: 'primary.main',
                        color: 'white',
                        padding: '12px',
                        '&:hover': {
                            backgroundColor: 'primary.dark',
                        },
                    }}
                >
                    <CloudUploadIcon sx={{ fontSize: 40 }} />
                </IconButton>
            </label>

            {isLoading && <CircularProgress sx={{ marginTop: 2 }} />}

            {filePreviews && (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        gap: 2,
                        mt: 2,
                    }}
                >
                    <Box sx={{ position: 'relative' }}>
                        <img
                            src={filePreviews}
                            alt={`Preview`}
                            style={{
                                width: '150px',
                                height: '150px',
                                objectFit: 'contain',
                                borderRadius: '8px',
                            }}
                        />
                        <Button
                            variant="contained"
                            color="secondary"
                            onClick={() => handleRemoveFile()}
                            sx={{
                                position: 'absolute',
                                top: 4,
                                right: 4,
                                minWidth: '32px',
                                minHeight: '32px',
                                padding: 0,
                            }}
                        >
                            X
                        </Button>
                    </Box>
                </Box>
            )}
        </Box>
    );
};

export default SingleFileInput;
