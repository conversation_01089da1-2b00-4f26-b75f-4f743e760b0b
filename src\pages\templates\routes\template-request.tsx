import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { TableComponent } from 'components/table/table.tsx';
import Paper from '@mui/material/Paper';
import { InputBase } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import useAllRequests from 'pages/templates/hooks/useAllRequests.ts';
import { RequestDto } from 'pages/templates/types/requests.ts';
import Button from '@mui/material/Button';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import rejectRequest from 'pages/templates/api/rejectRequest.ts';
import approveRequest from 'pages/templates/api/approveRequest.ts';
import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";

export default function TemplateRequest() {
  const [searchValue, setSearchValue] = useState('');
  const { data, loading, refetch } = useAllRequests();

  const [actionLoading, setActionLoading] = useState<boolean>(false);

  const columns = useMemo<ColumnDef<RequestDto>[]>(
    () => [
      {
        header: '#',
        accessorFn: (_, index) => `${index + 1}`,
        id: '_index',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Username',
        accessorFn: (row) => row.seller.name,
        id: 'seller.name',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Title',
        accessorFn: (row) => row.title,
        id: 'title',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Regional Limit',
        accessorFn: (row) => row.region,
        id: 'region',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Platform',
          accessorFn: (row) => row.category?.categoryName?.en || '',
            id: 'category.categoryName.en',
            cell: (info) => info.getValue(),
      },
        {
            header: 'Language',
            accessorFn: (row) => row.language.join(', '),
            id: 'language',
            cell: (info) => (
                <>
                    {info.row.original.language.map((item, index) => (
                        <span key={index}>
                            {item}
                            {index < info.row.original.language.length - 1 && ', '}
                        </span>
                    ))}
                </>
            ),
        },
      {
        header: 'Date',
        accessorFn: (row) => row.createdAt,
        id: 'created_at',
        cell: (info) => <span>{info.row.original.createdAt.split('T')[0]}</span>,
      },
      {
        header: 'Status',
        accessorFn: (row) => row.isApproved,
        id: 'isApproved',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Action',
        accessorFn: () => {},
        id: 'action',
        cell: (info) => (
          <Stack direction="row" spacing={1} alignItems="center">
              {info.row.original.isApproved === 'waiting' && <>
                  <Button
                      onClick={() => handleActions(info.row.original._id, 'accept')}
                      sx={{ padding: 0, minWidth: 0 }}
                  >
                      <IconifyIcon icon="ion:checkmark-circle-outline" sx={{ fontSize: '20px' }} />
                  </Button>
                  <Button
                      sx={{ padding: 0, minWidth: 0 }}
                      onClick={() => handleActions(info.row.original._id, 'reject')}
                  >
                      <IconifyIcon icon="ion:close-outline" sx={{ fontSize: '20px' }} onClick={() => {}} />
                  </Button>
              </>}
          </Stack>
        ),
      },
    ],
    [],
  );

  const handleActions = async (_id: string, type: string) => {
      setActionLoading(true);
        if (type === 'reject') await rejectRequest({ _id });
        else await  approveRequest({ _id });
        await refetch();
      setActionLoading(false);
  };

  if (loading || !data) {
    return <CircularProgress />;
  }

  return (
    <Stack direction="column" spacing={2}>
      <Typography
        variant="h5"
        fontWeight={600}
        letterSpacing={1}
        fontFamily={fontFamily.workSans}
        display={{ xs: 'none', lg: 'block' }}
      >
        Request Template List
      </Typography>
      <Paper
        component="form"
        sx={{
          p: '2px 4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: {xs: "100%", sm: 400},
          marginLeft: 'auto',
        }}
      >
        <InputBase
          sx={{ ml: 1, flex: 1, border: 'none' }}
          placeholder="Search here"
          onChange={e=> setSearchValue(e.target.value)}
          inputProps={{ 'aria-label': 'search google maps' }}
        />
        <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
          <SearchIcon />
        </IconButton>
      </Paper>

        <Box sx={{ overflowX: 'auto', width: '100%' }}>
            {actionLoading && <CircularProgress />}
          <TableComponent
            columns={columns}
            data={data}
            globalFilter={searchValue}
            setGlobalFilter={setSearchValue}
          />
        </Box>
    </Stack>
  );
}