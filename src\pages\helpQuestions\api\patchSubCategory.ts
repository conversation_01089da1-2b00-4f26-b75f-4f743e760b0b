import { patchRequest } from 'vbrae-utils';

type Props = {
    title: string;
    isActive: boolean;
    categoryId: string;
    subcategoryId: string;
};

export async function patchSubCategory(props: Props): Promise<{message: string}> {

    const r = await patchRequest<Props>({
        url: `help/${props.categoryId}/subcategories/${props.subcategoryId}`,
        data: props,
        useAuth: true,
    });
    return r.response;
}