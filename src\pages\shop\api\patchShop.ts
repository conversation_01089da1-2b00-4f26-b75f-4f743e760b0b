import { patchRequest } from 'vbrae-utils';

interface Response {
  status: string;
}

interface PatchShopDto{
    _id: string;
    isApproved: string;
}

export async function patchShop({ _id, isApproved }: PatchShopDto): Promise<Response> {
    const r = await patchRequest({
        url: `shop-opening-requests/${_id}`,
        data: { _id, isApproved },
        useAuth: true,
    });

    return r.response;
}
