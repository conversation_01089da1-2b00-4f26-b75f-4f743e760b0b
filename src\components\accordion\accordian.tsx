import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import Chip from '@mui/material/Chip';
import React from 'react';
import { AccordionDto } from 'pages/category/types/accordion.ts';

interface CustomAccordionProps {
  data: AccordionDto[];
  onParentDelete: (item: { _id: string }) => void;
  onChildDelete: (item: { _id: string }) => void;
  onParentEdit: (item: { _id: string }) => void;
  onChildEdit: (item: { _id: string }) => void;
}

const CustomAccordion: React.FC<CustomAccordionProps> = ({ data, onChildEdit, onParentEdit, onChildDelete, onParentDelete }) => {
    const renderAccordion = (item: AccordionDto, isChild = false) => {

        const deleteFunction = isChild ? onChildDelete : onParentDelete;
        const editFunction = isChild ? onChildEdit : onParentEdit;

        return (
            <Accordion key={item.title} defaultExpanded={false}>
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${item.title.replace(/\s+/g, '-').toLowerCase()}-title`}
                    id={`${item.title.replace(/\s+/g, '-').toLowerCase()}-header`}
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: 0,
                        alignItems: {
                            xs: "start",
                        },
                        flexDirection: {
                            xs: "column",
                            sm: "row"
                        },
                        "& .MuiAccordionSummary-content": {
                            width: {
                                xs: '100%',
                                sm: 'auto',
                            },
                        },
                    }}
                >
                    <Typography
                        component="span"
                        mr={4}
                        sx={{ fontSize: { xs: "12px", sm: "14px" } }}
                    >
                        {item.title}
                    </Typography>
                    <Typography fontSize="12px"
                                component="span"
                                sx={{display: {
                                    xs: "none", sm: "block"
                                    }}}>
                        {item._id}
                    </Typography>
                    <Box sx={{ marginLeft: 'auto' }}>
                        {item.visibility && (
                            <Chip
                                label="Visible"
                                color="primary"
                                sx={{
                                    margin: '2px',
                                    borderRadius: '16px',
                                    padding: '6px 12px',
                                    color: '#333',
                                    fontSize: {
                                        xs: '12px',
                                        sm: '14px'
                                    },
                                    '& .MuiChip-deleteIcon': {
                                        marginLeft: '8px',
                                        color: '#777',
                                    },
                                }}
                            />
                        )}
                        {item.showInMainMenu && (
                            <Chip
                                label="Main Menu"
                                color="success"
                                sx={{
                                    margin: '2px',
                                    borderRadius: '16px',
                                    padding: {
                                        sm: "6px 12px",
                                        xs: "2px 6px"
                                    },
                                    color: '#333',
                                    fontSize: {
                                        xs: '12px',
                                        sm: '14px'
                                    },
                                    '& .MuiChip-deleteIcon': {
                                        marginLeft: '8px',
                                        color: '#777',
                                    },
                                }}
                            />
                        )}
                        <Button
                            sx={{ padding: 0, minWidth: {xs: 24, lg: 64}}}
                            onClick={(event) => {
                                event.stopPropagation();
                                editFunction({ _id: item._id })
                            }}>
                            <IconifyIcon
                                icon={'ion:create-outline'}
                                sx={{ fontSize: '20px' }}
                            />
                        </Button>
                        <Button
                            sx={{ padding: 0, minWidth: {xs: 24, lg: 64}}}
                            onClick={(event) => {
                                event.stopPropagation();
                                deleteFunction({_id: item._id})
                            }}>
                            <IconifyIcon
                                icon={'ion:trash-outline'}
                                sx={{ fontSize: '20px' }}
                            />
                        </Button>
                    </Box>
                </AccordionSummary>
                {!isChild &&
                    item.children &&
                    item.children.map((child) => renderAccordion(child as AccordionDto, true))}
            </Accordion>
        );
    };

    return (
        <Stack direction="column" spacing={3} my={5}>
            {data.map((item) => renderAccordion(item))}
        </Stack>
    );
};

export default CustomAccordion;
