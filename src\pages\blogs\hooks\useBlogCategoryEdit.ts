import * as Yup from 'yup';
import { useMutation, useQueryClient } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { SelectedValue } from 'components/select-menu.tsx';
import { languages } from 'pages/category/const/languages.ts';
import { useEffect, useState } from 'react';
import { patchBlogCategory } from 'pages/blogs/api/patch-blog-category.ts';
import {BlogCategory} from "pages/blogs/types/blog-category.ts";

export type ErrorType = {
  message: string;
};

const blogCategorySchema = Yup.object().shape({
    language: Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string().optional(),
    }),
    categoryName: Yup.string().required('Category Name is required'),
    description: Yup.string().required('Description is required'),
    keywords: Yup.string().required('Meta Keywords are required'),
    order: Yup.number().required('Order is required'),
});

type InitialValuesDto = {
    language: SelectedValue;
    categoryName: string;
    description: string;
    keywords: string;
    order: number;
};

export default function useBlogCategoryEdit({categoryDetails}: {categoryDetails?: BlogCategory}) {
    const queryClient = useQueryClient();
    const [initialValues, setInitialValues] = useState<InitialValuesDto>({
        language: languages[1],
        categoryName: "",
        description: "",
        keywords: "",
        order: 1,
    });

    useEffect(() => {
        if(!categoryDetails) return;

        setInitialValues({
            language: languages.find(lang => lang._id === categoryDetails.language) || { _id: '', name: '' },
            categoryName: categoryDetails.categoryName,
            description: categoryDetails.description,
            keywords: categoryDetails.keywords.join(","),
            order: categoryDetails.order,
        });
    }, [categoryDetails]);

    const { mutateAsync } = useMutation(patchBlogCategory, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: blogCategorySchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;
            if(categoryDetails) response = await mutateAsync({ ...values, language: values.language.name, keywords: values.keywords.split(","), _id: categoryDetails._id });
            setSubmitting(false);
            resetForm();
            if (response) {
                queryClient.invalidateQueries("blog-categories").finally()
                showSuccess("Category Added Successfully.")
            }
        },
    });

    return { formik };
}

