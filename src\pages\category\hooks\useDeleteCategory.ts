import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteCategories } from 'pages/category/api/deleteCategory.ts';

export default function useDeleteCategory(props: {_id: string}) {
  const queryClient = useQueryClient();
  const { refetch: categoryDelete, loading: categoryDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-categories', { ...props }],
      queryFn: () => deleteCategories(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['categories']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { categoryDelete, categoryDelLoading };
}