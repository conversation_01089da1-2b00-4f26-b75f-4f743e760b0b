import { useEffect, useState } from 'react';
export function useQueryFix({ query, transform, }) {
    const { isLoading, error, data, refetch } = query;
    const [localData, setLocalData] = useState(data ? transform(data) : undefined);
    const [localLoading, setLocalLoading] = useState(isLoading);
    useEffect(() => {
        if (isLoading) {
            setLocalLoading(true);
        }
        if (error && localLoading) {
            setLocalLoading(false);
        }
        if (data) {
            setLocalData(transform(data));
            setLocalLoading(false);
        }
    }, [isLoading, error, data]);
    const handleRefetch = async () => {
        await refetch();
    };
    return {
        data: localData,
        loading: localLoading,
        error,
        refetch: handleRefetch,
    };
}
