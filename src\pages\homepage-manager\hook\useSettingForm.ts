import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { patchSetting } from 'pages/homepage-manager/api/patchSetting.ts';
import { useEffect, useState } from 'react';
import { ConfigData } from 'pages/homepage-manager/types/homepage-manager.ts';

export type ErrorType = {
  message: string;
};

const settingSchema = Yup.object().shape({
    topOffers:Yup.object().shape({
        enabled: Yup.string(),
        limit: Yup.number().required("Limit is required"),
    }),
    softwares:Yup.object().shape({
        enabled: Yup.string(),
        limit: Yup.number().required("Limit is required"),
    }),
    newArrivals:Yup.object().shape({
        enabled: Yup.string(),
        limit: Yup.number().required("Limit is required"),
    }),
    articles:Yup.object().shape({
        enabled: Yup.string(),
        limit: Yup.number().required("Limit is required"),
    }),
});

type InitialValuesDto = {
    topOffers:{
        enabled: boolean;
        limit: number;
    };
    softwares:{
        enabled: boolean;
        limit: number;
    },
    newArrivals:{
        enabled: boolean;
        limit: number;
    },
    articles:{
        enabled: boolean;
        limit: number;
    },
};

export default function useSettingForm({settings}: {settings?: ConfigData}) {

    const [initialValues, setInitialState] = useState<InitialValuesDto>({
        topOffers:{
            enabled: true,
            limit: 10,
        },
        softwares:{
            enabled: true,
            limit: 10,
        },
        newArrivals:{
            enabled: true,
            limit: 10,
        },
        articles:{
            enabled: true,
            limit: 10,
        },
    });

    useEffect(() => {
        if(!settings) return;

        setInitialState({
            topOffers:settings.topOffers,
            softwares:settings.softwares,
            newArrivals:settings.newArrivals,
            articles:settings.articles,
        })
    }, [settings]);
    
    const { mutateAsync } = useMutation(patchSetting, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: settingSchema,
        onSubmit: async (values, { setSubmitting }) => {
            setSubmitting(true);

            const response = await mutateAsync(values);
            setSubmitting(false);
            if (response) {
                showSuccess("Configuration added successfully.");
            }
        },
    });

    return { formik };
}
