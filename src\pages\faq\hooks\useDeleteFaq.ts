import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteFaq } from 'pages/faq/api/deleteFaq.ts';

export default function useDeleteFaq(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: faqDelete, loading: faqDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-faq', { ...props }],
      queryFn: () => deleteFaq(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['faqs']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { faqDelete, faqDelLoading };
}