//get home configs

export interface ConfigResponse {
    status: string;
    data: ConfigData;
}

export interface ConfigData {
    topOffers: {
        enabled: boolean;
        limit: number;
    };
    softwares: {
        enabled: boolean;
        limit: number;
    };
    newArrivals: {
        enabled: boolean;
        limit: number;
    };
    articles: {
        enabled: boolean;
        limit: number;
    };
    _id: string;
}

// home config form

export interface KeyValues {
    _id?: string;
    open: boolean;
}

// get all banners
export interface BannerResponseDto {
    status: string;
    data: {
        banners: BannerDto[]
    };
}

export interface BannerDto {
    images: {
        desktop: string;
        tablet: string;
        phone: string;
    };
    tag: string;
    title: string;
    logoImage?: string;
    shortSummary: string;
    discountPercent: number;
    existingPrice: number;
    couponCode: string;
    order: number;
    link: string;
    category: string;
    _id: string;
    startTime: string | null;
    endTime: string | null;
}