import {getProfile} from "../api/getProfile.ts";
import {useQuery} from "react-query";
import {getAccessToken, showError, useQueryFix} from "vbrae-utils";

export const useProfile = () => {
  const hasUser = !!getAccessToken();
  const { data: user, loading: userLoading } = useQueryFix({
    query: useQuery({
      queryKey: 'user-profile',
      queryFn: () => getProfile(),
      onError: showError,
      staleTime: 10000,
      enabled: hasUser,
      refetchOnWindowFocus: false,
    }),
    transform: (data) => data.user,
  });

  return { user, userLoading, hasUser};
};