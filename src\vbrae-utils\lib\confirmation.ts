import Swal from "sweetalert2";

interface ConfirmationProps {
  handleDelete: (arg:string)=> string;
  id: string;
}

export const showConfirmation = ({ handleDelete, id }:ConfirmationProps) => {
  Swal.fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    showCancelButton: true,
    background: '#171E2E',
    confirmButtonColor: '#2DC071',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, delete it!',
  }).then((result: { isConfirmed: boolean }) => {
    if (result.isConfirmed) {
      handleDelete(id);
    }
  });
};