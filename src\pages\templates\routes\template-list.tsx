import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { InputBase } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import useAllTemplates from 'pages/templates/hooks/useAllTemplates.ts';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router-dom';
import Stack from '@mui/material/Stack';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import useRemoveTemplate from 'pages/templates/hooks/useRemoveTemplate.ts';
import {TemplateDto} from "pages/templates/types/template.ts";
import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";

export default function TemplateList() {
  const { data, loading } = useAllTemplates();
  const navigate = useNavigate();
  const [templateId, setTemplateId] = useState<string>('');
  const { templateDelete, templateLoading } = useRemoveTemplate({ _id: templateId });

  const [searchValue, setSearchValue] = useState('');
  const columns = useMemo<ColumnDef<TemplateDto>[]>(
    () => [
      {
        header: 'Sr. No',
        accessorFn: (_, index) => `${index + 1}`,
        id: '_index',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Title',
        accessorFn: (row) => row.templateName,
        id: 'templateName',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Genres',
        accessorFn: (row) => row.genres,
        id: 'genres',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Status',
        accessorFn: (row) => row.active ? 'Active' : 'Disabled',
        id: 'active',
        cell: (info) => <Typography>{info.row.original.active ? 'Active' : 'Disabled'}</Typography>,
        enableGlobalFilter: true,
      },
      {
        header: 'Action',
        accessorFn: () => {},
        id: 'action',
        cell: (info) => (
          <Stack direction="row" spacing={1} alignItems="center">
            {/*<Button sx={{ padding: 0, minWidth: 0 }}>*/}
            {/*  <IconifyIcon icon="ion:copy-outline" sx={{ fontSize: '20px' }} />*/}
            {/*</Button>*/}
            <Button
              onClick={() => navigate(`/templates/${info.row.original._id}`)}
              sx={{ padding: 0, minWidth: 0 }}
            >
              <IconifyIcon icon="ion:create-outline" sx={{ fontSize: '20px' }} />
            </Button>
            <Button sx={{ padding: 0, minWidth: 0 }} disabled={templateLoading}>
                <IconifyIcon
                    icon="ion:trash-outline"
                    sx={{ fontSize: '20px' }}
                    onClick={() => setTemplateId(info.row.original._id)}
                />
            </Button>
          </Stack>
        ),
        enableGlobalFilter: false, // Actions don't need to be globally searchable
      },
    ],
    [],
  );

  useEffect(() => {
    if (!templateId) return;

    templateDelete().finally();
  }, [templateId]);

  if (loading || !data) return <CircularProgress />;
  return (
    <Stack direction="column" spacing={2}>
      <Typography
        variant="h5"
        fontWeight={600}
        letterSpacing={1}
        fontFamily={fontFamily.workSans}
        display={{ xs: 'none', lg: 'block' }}
      >
        Offer List
      </Typography>
      <Paper
        component="form"
        sx={{
          p: '2px 4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: {xs: "100%", sm: 400},
          marginLeft: 'auto',
        }}
      >
        <InputBase
          sx={{ ml: 1, flex: 1, border: 'none' }}
          placeholder="Search here"
          inputProps={{ 'aria-label': 'search google maps' }}
          onChange={(e) => setSearchValue(e.target.value.trim())}
        />
        <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
          <SearchIcon />
        </IconButton>
      </Paper>

        <Box sx={{ overflowX: 'auto', width: '100%' }}>
            {templateLoading && <CircularProgress />}
            <TableComponent
                columns={columns}
                data={data}
                globalFilter={searchValue}
                setGlobalFilter={setSearchValue}
            />
        </Box>
    </Stack>
  );
}