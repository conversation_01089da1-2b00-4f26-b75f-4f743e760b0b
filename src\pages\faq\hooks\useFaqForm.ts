import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { useEffect, useState } from 'react';
import { FAQ } from 'pages/faq/types/FAQ.ts';
import { postFaq } from 'pages/faq/api/postFaq.ts';
import { patchFaq } from 'pages/faq/api/patchFaq.ts';

export type ErrorType = {
  message: string;
};

const faqSchema = Yup.object().shape({
    title:Yup.string().trim(),
    content: Yup.string(),
    page: Yup.string(),
    order: Yup.number(),
});

export type InitialValuesDto = {
    title: string;
    content: string;
    page: string;
    order: number;
};

export default function useFaqForm({existingFaq}: {existingFaq?: FAQ}) {
    const [initialValues, setInitialState] = useState<InitialValuesDto>({
        title: '',
        content: '',
        page: 'about',
        order: 1
    });

    useEffect(() => {
        if(!existingFaq) return;

        setInitialState({
            title: existingFaq.title,
            content: existingFaq.content,
            page: 'about',
            order: existingFaq.order
        })
    }, [existingFaq]);

    const { mutateAsync } = useMutation(postFaq, {
        onError: (error:ErrorType)=>showError(error),
    });

    const { mutateAsync:updateAsync } = useMutation(patchFaq, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: faqSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;
            if(existingFaq) response = await updateAsync({ _id: existingFaq._id, ...values });
            else response = await mutateAsync(values);
            setSubmitting(false);
            !existingFaq && resetForm();
            if (response) {
                showSuccess(`Faq ${existingFaq ? 'updated' : 'added'} successfully`)
            }
        },
    });

    return { formik };
}
