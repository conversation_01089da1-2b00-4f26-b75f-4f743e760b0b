import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteBlogCategory } from 'pages/blogs/api/delete-blog-category.ts';

export default function useDeleteBlogCategory(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: categoryDelete, loading: categoryDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-blog-category', { ...props }],
      queryFn: () => deleteBlogCategory(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['blog-categories']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { categoryDelete, categoryDelLoading };
}