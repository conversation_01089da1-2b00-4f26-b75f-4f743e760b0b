import { useQuery } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import { getMessages } from '../api/getMessages';
import { showError, useQueryFix } from 'vbrae-utils';

export const useMessages = () => {
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get('conversationId') ?? "";

  const { data: messages, loading: messagesLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['messages', conversationId],
      queryFn: () => getMessages({ conversationId }),
      onError: showError,
      refetchOnWindowFocus: true,
      enabled: !!conversationId,
    }),
    transform: (data) => data.messages,
  });

  return { messages, messagesLoading };
};