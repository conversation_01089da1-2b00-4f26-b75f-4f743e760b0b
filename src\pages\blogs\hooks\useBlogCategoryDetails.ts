import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import getBlogCategoryDetails from 'pages/blogs/api/get-blog-category-details.ts';

export default function useBlogCategoryDetails() {
  const { id = '' } = useParams();
  const { data: categoryDetails, loading: categoryDetailsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['sub-categories-details', id],
      queryFn: () => getBlogCategoryDetails({ id }),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { categoryDetails, categoryDetailsLoading };
}
