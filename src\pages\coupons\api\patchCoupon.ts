import { patchRequest } from 'vbrae-utils';
import { InitialValuesDto } from 'pages/coupons/hooks/useCouponForm.ts';

interface Response {
  status: string;
}

interface ExtendedInitialValuesDto extends InitialValuesDto {
    _id: string;
}

export async function patchCoupon({ _id, ...otherProps }: ExtendedInitialValuesDto): Promise<Response> {
  const r = await patchRequest({
    url: `coupon/${_id}`,
    data: otherProps,
    useAuth: true,
  });

  return r.response;
}
