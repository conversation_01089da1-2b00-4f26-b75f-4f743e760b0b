import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from 'react/jsx-runtime';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import List from '@mui/material/List';
import Stack from '@mui/material/Stack';
import ButtonBase from '@mui/material/ButtonBase';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import Image from 'components/base/Image';
import IconifyIcon from 'components/base/IconifyIcon';
import ListItem from './list-items/ListItem';
import LogoImg from 'assets/images/Logo.png';
import { categoryOptions, sideMenuTop } from 'routes/sitemap.ts';
import CollapseListItem from 'layouts/main-layout/sidebar/list-items/CollapseListItem.tsx';
import { useLocation } from 'react-router-dom';
const DrawerItems = () => {
  const location = useLocation();
  return _jsxs(_Fragment, {
    children: [
      _jsx(Stack, {
        pt: 5,
        pb: 4,
        px: 3.5,
        position: 'sticky',
        top: 0,
        bgcolor: 'info.darker',
        alignItems: 'center',
        justifyContent: 'flex-start',
        zIndex: 1000,
        children: _jsxs(ButtonBase, {
          component: Link,
          href: '/',
          disableRipple: true,
          children: [
            _jsx(Image, { src: LogoImg, alt: 'logo', height: 24, width: 24, sx: { mr: 1 } }),
            _jsx(Typography, {
              variant: 'h5',
              color: 'text.primary',
              fontWeight: 600,
              letterSpacing: 1,
              children: 'Dashdark X',
            }),
          ],
        }),
      }),
      _jsx(Box, {
        px: 3.5,
        pb: 3,
        pt: 1,
        children: _jsx(TextField, {
          variant: 'filled',
          placeholder: 'Search for...',
          sx: { width: 1 },
          InputProps: {
            startAdornment: _jsx(InputAdornment, {
              position: 'start',
              children: _jsx(IconifyIcon, { icon: 'mingcute:search-line' }),
            }),
          },
        }),
      }),
      _jsx(List, {
        component: 'nav',
        sx: { px: 2.5 },
        children: sideMenuTop.map((route) =>
          _jsx(ListItem, { active: location.pathname === route?.path, ...route }, route.id),
        ),
      }),
      _jsx(List, {
        component: 'nav',
        sx: { px: 2.5 },
        children: categoryOptions.map((route) => {
          if (route.items) {
            const isActive = route.items.some((item) => location.pathname.includes(item.path));
            return _jsx(CollapseListItem, { active: isActive, ...route, location }, route.id);
          }
          return _jsx(ListItem, { ...route }, route.id);
        }),
      }),
    ],
  });
};
export default DrawerItems;
