Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x1FEBA
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210285FF9, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DD0  0002100690B4 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0B0  00021006A49D (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC1A280000 ntdll.dll
7FFC18DC0000 KERNEL32.DLL
7FFC17C30000 KERNELBASE.dll
7FFC18370000 USER32.dll
7FFC17950000 win32u.dll
7FFC18210000 GDI32.dll
7FFC17610000 gdi32full.dll
7FFC17750000 msvcp_win.dll
7FFC17800000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC18AD0000 advapi32.dll
7FFC18C00000 msvcrt.dll
7FFC18240000 sechost.dll
7FFC190E0000 RPCRT4.dll
7FFC168E0000 CRYPTBASE.DLL
7FFC17A10000 bcryptPrimitives.dll
7FFC19FA0000 IMM32.DLL
