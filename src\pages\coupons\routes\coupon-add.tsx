import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router-dom';
import { FormControl, InputLabel, Radio, RadioGroup } from '@mui/material';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import FormControlLabel from '@mui/material/FormControlLabel';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import useCategories from 'pages/category/hooks/useCategories.ts';
import CircularProgress from '@mui/material/CircularProgress';
import Checkbox from '@mui/material/Checkbox';
import { generateCouponCode } from 'pages/coupons/functions/coupon.ts';
import useCouponForm from 'pages/coupons/hooks/useCouponForm.ts';
import {ChangeEvent} from "react";
import dayjs from "dayjs";

export default function CouponAdd() {
  const navigate = useNavigate();
  const { categories, categoryLoading } = useCategories({});
  const { formik } = useCouponForm({});

  if (categoryLoading || !categories) {
    return <CircularProgress />;
  }

  const handleSelect = (e:ChangeEvent<HTMLInputElement>) => {
      const isChecked = e.target.checked;
      formik.setFieldValue('appliesToAllCategories', isChecked);
      const allCategoriesId = isChecked ? categories.map(item=> item._id) : [];
      formik.setFieldValue('applicableCategories', allCategoriesId);
  }

    const handleCategorySelect = (e: ChangeEvent<HTMLInputElement>) => {
        const { checked, id } = e.target;
        const currentCategories = formik.values.applicableCategories || [];
        let updatedCategories;
        if (checked) updatedCategories = [...currentCategories, id];
        else updatedCategories = currentCategories.filter(categoryId => categoryId !== id);
        formik.setFieldValue("applicableCategories", updatedCategories);
    };

    return (
    <Stack direction="column" spacing={2}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Add Coupons
        </Typography>
        <Button type="submit" variant="contained" size="small" onClick={() => navigate(`/coupons`)}>
          All Coupon
        </Button>
      </Stack>

      <form onSubmit={formik.handleSubmit}>
          <Stack direction="column" gap={2} width="50%">
              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="code"
                  >
                      Coupon Code <span style={{ fontSize: '10px' }}>(Do not use special characters)</span>
                  </InputLabel>
                  <TextField
                      id="code"
                      type="text"
                      variant="filled"
                      placeholder="Coupon Code"
                      autoComplete="code"
                      fullWidth
                      required
                      {...formik.getFieldProps('code')}
                  />
                  <Button
                      variant="outlined"
                      size="small"
                      sx={{ marginTop: 1 }}
                      onClick={() => formik.setFieldValue('code', generateCouponCode())}
                  >
                      Generate
                  </Button>
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="discountValue"
                  >
                      Discount Rate
                  </InputLabel>
                  <TextField
                      id="discountValue"
                      type="number"
                      variant="filled"
                      placeholder="E.g : 5"
                      autoComplete="off"
                      fullWidth
                      sx={{
                          '& .MuiInputBase-root': {
                              padding: '10px 6px',
                          },
                      }}
                      required
                      InputProps={{
                          startAdornment: (
                              <InputAdornment position="start">
                                  <Box
                                      sx={{
                                          background: '#f0f0f0',
                                          padding: '6px 10px',
                                          borderRadius: '4px 0 0 4px',
                                          fontSize: '14px',
                                          fontWeight: 'bold',
                                      }}
                                  >
                                      %
                                  </Box>
                              </InputAdornment>
                          ),
                      }}
                      {...formik.getFieldProps('discountValue')}
                  />
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="totalCoupons"
                  >
                      Number of coupons
                      <span style={{ fontSize: '10px' }}>
              (How many times a coupon can be used by all customers before being invalid)
            </span>
                  </InputLabel>
                  <TextField
                      id="totalCoupons"
                      type="number"
                      variant="filled"
                      placeholder="E.g : 100"
                      autoComplete="code"
                      fullWidth
                      required
                      {...formik.getFieldProps('totalCoupons')}
                  />
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="discount"
                  >
                      Minimum order amount
                      <span style={{ fontSize: '10px' }}>(Minimum cart total needed to use the coupon)</span>
                  </InputLabel>
                  <TextField
                      id="discount"
                      type="number"
                      variant="filled"
                      placeholder="0.0"
                      autoComplete="off"
                      fullWidth
                      sx={{
                          '& .MuiInputBase-root': {
                              padding: '10px 6px',
                          },
                      }}
                      required
                      InputProps={{
                          startAdornment: (
                              <InputAdornment position="start">
                                  <Box
                                      sx={{
                                          background: '#f0f0f0',
                                          padding: '6px 10px',
                                          borderRadius: '4px 0 0 4px',
                                          fontSize: '14px',
                                          fontWeight: 'bold',
                                      }}
                                  >
                                      $
                                  </Box>
                              </InputAdornment>
                          ),
                      }}
                      {...formik.getFieldProps('minOrderAmount')}
                  />
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
                      size="small"
                      htmlFor="sort_categories"
                  >
                      Coupon usage type
                  </InputLabel>
                  <FormControl>
                      <RadioGroup
                          row
                          aria-labelledby="demo-row-radio-buttons-group-label"
                          onChange={(e) => formik.setFieldValue('couponUserType', e.currentTarget.value)}
                      >
                          <FormControlLabel
                              value="single"
                              control={<Radio />}
                              label="Each user can use it for only one order"
                              checked={formik.values.couponUserType === 'single'}
                          />
                          <FormControlLabel
                              value="multiple"
                              control={<Radio />}
                              label="Each user can use it for multiple orders"
                              checked={formik.values.couponUserType === 'multiple'}
                          />
                      </RadioGroup>
                  </FormControl>
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="createdAt"
                  >
                      Creation Date
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DatePicker
                              sx={{
                                  display: 'block',
                                  '& .MuiInputBase-root': {
                                      width: '100%',
                                  },
                                  '& fieldset': {
                                      border: 'none',
                                  },
                              }}
                              minDate={dayjs()}
                              label={null}
                              onChange={(newValue) =>
                                  formik.setFieldValue('expirationDate', newValue?.toISOString())
                              }
                          />
                      </LocalizationProvider>
                  </InputLabel>
              </Box>

              <Box>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px', display: 'flex', alignItems: 'center' }}
                      size="small"
                      htmlFor="products"
                  >
                      Products
                      <Checkbox
                          inputProps={{ 'aria-label': 'all' }}
                          checked={formik.values.appliesToAllCategories}
                          onChange={handleSelect}
                      />
                  </InputLabel>
                  {categories.map((category) => (
                      <Box display="flex" alignItems="center" gap="10px" key={category._id}>
                          <Checkbox
                              id={category._id}
                              inputProps={{ 'aria-label': category._id }}
                              checked={formik.values.applicableCategories.includes(category._id)}
                              onChange={handleCategorySelect}
                          />
                          <InputLabel
                              component="label"
                              sx={{ fontSize: '12px', marginBottom: '20px' }}
                              size="small"
                              htmlFor="products"
                          >
                              {category.categoryName.en}
                          </InputLabel>
                      </Box>
                  ))}
              </Box>

              <Button type="submit" variant="contained" size="medium">
                  {formik.isSubmitting ? 'Processing...' : 'Add Coupon'}
              </Button>
          </Stack>
      </form>
    </Stack>
  );
}