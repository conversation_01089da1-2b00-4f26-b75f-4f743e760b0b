import React, { useState } from 'react';
import { Button, Menu, MenuItem } from '@mui/material';
import IconifyIcon from 'components/base/IconifyIcon.tsx';

interface OptionsDto {
  title: string;
  action: () => void;
  disabled?: boolean;
}

export default function BasicDropdown({options}: {options: OptionsDto[]}) {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <div>
            <Button
                variant="outlined"
                onClick={handleClick}
            >
                <IconifyIcon icon="mingcute:more-2-line" />
            </Button>
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
            >
                {options.map((option, index) => (
                    <MenuItem
                        key={index}
                        disabled={option.disabled}
                        onClick={() => {
                            if (!option.disabled) {
                                option.action();
                                handleClose();
                            }
                        }}
                    >
                        {option.title}
                    </MenuItem>
                ))}
            </Menu>
        </div>
    );
}
