import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getTemplateDetails } from 'pages/templates/api/getTemplateDetails.ts';
import templateDetailsTransformer from "pages/templates/hooks/transformer/templaetDetailsTransformer.ts";
export default function useTemplateDetails(props) {
    const { data: templateDetails, loading } = useQueryFix({
        query: useQuery({
            queryKey: ['templates-details', props._id],
            queryFn: () => getTemplateDetails(props),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => templateDetailsTransformer(data),
    });
    return { templateDetails, loading };
}
