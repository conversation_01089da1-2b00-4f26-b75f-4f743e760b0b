import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteBlog } from 'pages/blogs/api/deleteBlog.ts';

export default function useDeleteBlog(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: blogDelete, loading: blogDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-blog', { ...props }],
      queryFn: () => deleteBlog(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['blogs']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { blogDelete, blogDelLoading };
}