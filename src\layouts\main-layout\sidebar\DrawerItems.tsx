import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import List from '@mui/material/List';
import Stack from '@mui/material/Stack';
import ButtonBase from '@mui/material/ButtonBase';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import Image from 'components/base/Image';
import IconifyIcon from 'components/base/IconifyIcon';
import ListItem from './list-items/ListItem';
import LogoImg from 'assets/images/Logo.png';
import { categoryOptions, sideMenuTop } from 'routes/sitemap.ts';
import CollapseListItem from 'layouts/main-layout/sidebar/list-items/CollapseListItem.tsx';
import {useLocation} from "react-router-dom";

const DrawerItems = () => {
    const location = useLocation();

    console.log("called")

      return (
        <>
          <Stack
            pt={5}
            pb={4}
            px={3.5}
            position={'sticky'}
            top={0}
            bgcolor="info.darker"
            alignItems="center"
            justifyContent="flex-start"
            zIndex={1000}
          >
            <ButtonBase component={Link} href="/" disableRipple>
              <Image src={LogoImg} alt="logo" height={24} width={24} sx={{ mr: 1 }} />
              <Typography variant="h5" color="text.primary" fontWeight={600} letterSpacing={1}>
                Dashdark X
              </Typography>
            </ButtonBase>
          </Stack>

          <Box px={3.5} pb={3} pt={1}>
            <TextField
              variant="filled"
              placeholder="Search for..."
              sx={{ width: 1 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <IconifyIcon icon={'mingcute:search-line'} />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <List component="nav" sx={{ px: 2.5 }}>
            {sideMenuTop.map((route) => {
                return (
                    <ListItem key={route.id} active={location.pathname === route.path} {...route} />
                )
            })}
          </List>

          <List component="nav" sx={{ px: 2.5 }}>
            {categoryOptions.map((route) => {
              if (route.items) {
                return <CollapseListItem key={route.id} active={location.pathname.includes(route.id)} {...route} />;
              }
              return <ListItem key={route.id} {...route} />;
            })}
          </List>
        </>
      );
};

export default DrawerItems;
