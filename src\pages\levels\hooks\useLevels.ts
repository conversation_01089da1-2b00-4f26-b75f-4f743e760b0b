import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getLevels } from '../api/getLevels';

export default function useLevels() {
  const { data: levels, loading: levelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['levels'],
      queryFn: () => getLevels(),
      onError: showError,
    }),
    transform: (response) => response.data,
  });
  return { levels, levelLoading };
}
