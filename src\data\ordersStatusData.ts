import { GridRowsProp } from '@mui/x-data-grid';
import { formatNumber } from 'functions/formatNumber';

export const ordersStatusData: GridRowsProp = [
  {
    id: '#1532',
    client: { name: '<PERSON>', email: '<EMAIL>' },
    date: new Date('Jan 30, 2024'),
    status: 'delivered',
    country: 'United States',
    total: formatNumber(1099.24),
  },
  {
    id: '#1531',
    client: { name: '<PERSON>', email: '<EMAIL>' },
    date: new Date('Jan 27, 2024'),
    status: 'canceled',
    country: 'United Kingdom',
    total: formatNumber(5870.32),
  },
  {
    id: '#1530',
    client: { name: '<PERSON>', email: '<EMAIL>' },
    date: new Date('Jan 24, 2024'),
    status: 'delivered',
    country: 'Australia',
    total: formatNumber(13899.48),
  },
  {
    id: '#1529',
    client: { name: 'Graham <PERSON>', email: '<EMAIL>' },
    date: new Date('Jan 21, 2024'),
    status: 'pending',
    country: 'India',
    total: formatNumber(1569.12),
  },
  {
    id: '#1528',
    client: { name: '<PERSON>', email: 'contact@sandyhou<PERSON>.com' },
    date: new Date('Jan 18, 2024'),
    status: 'delivered',
    country: 'Canada',
    total: formatNumber(899.16),
  },
  {
    id: '#1527',
    client: { name: 'Andy Smith', email: '<EMAIL>' },
    date: new Date('Jan 15, 2024'),
    status: 'pending',
    country: 'United States',
    total: formatNumber(2449.64),
  },
  {
    id: '#1526',
    client: { name: 'Emma Grace', email: '<EMAIL>' },
    date: new Date('Jan 12, 2024'),
    status: 'delivered',
    country: 'Australia',
    total: formatNumber(6729.82),
  },
  {
    id: '#1525',
    client: { name: 'Ava Rose', email: '<EMAIL>' },
    date: new Date('Jan 09, 2024'),
    status: 'canceled',
    country: 'Canada',
    total: formatNumber(784.94),
  },
  {
    id: '#1524',
    client: { name: 'Olivia Jane', email: '<EMAIL>' },
    date: new Date('Jan 06, 2024'),
    status: 'pending',
    country: 'Singapur',
    total: formatNumber(1247.86),
  },
  {
    id: '#1523',
    client: { name: 'Mason Alexander', email: '<EMAIL>' },
    date: new Date('Jan 03, 2024'),
    status: 'delivered',
    country: 'United States',
    total: formatNumber(304.89),
  },
  {
    id: '#1522',
    client: { name: 'Samuel David', email: '<EMAIL>' },
    date: new Date('Jan 01, 2024'),
    status: 'pending',
    country: 'Japan',
    total: formatNumber(2209.76),
  },
  {
    id: '#1521',
    client: { name: 'Henry Joseph', email: '<EMAIL>' },
    date: new Date('Dec 28, 2023'),
    status: 'delivered',
    country: 'North Korea',
    total: formatNumber(5245.68),
  },
];
