import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { AccordionDetails, InputLabel } from '@mui/material';
import Paper from '@mui/material/Paper';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import { Editor } from '@tinymce/tinymce-react';
import { useRef } from 'react';
import { Editor as TinyMCEEditor } from 'tinymce';
import CircularProgress from '@mui/material/CircularProgress';
import { fontFamily } from 'theme/typography.ts';
import { editorProps } from 'constant/editor.ts';
import SingleFileInput from 'components/single-file-input.tsx';
import useBlogForm from 'pages/blogs/hooks/useBlogForm.ts';
import ChipInput from 'components/inputs/input-with-chip.tsx';
import { languages } from 'pages/category/const/languages.ts';
import useBlogCategories from 'pages/blogs/hooks/useBlogCategories.ts';
import useBlogDetails from 'pages/blogs/hooks/useBlogDetails.ts';
import { useParams } from 'react-router-dom';

export default function EditBlog() {
  const editorRef = useRef<TinyMCEEditor | null>(null);
  const germanEditorRef = useRef<TinyMCEEditor | null>(null);

  const { id = '' } = useParams();

  const { categories, categoryLoading } = useBlogCategories({ language: '' });
  const { details, detailsLoading } = useBlogDetails({ id });

  const { formik } = useBlogForm({
    blogDetails: details,
    onSubmit: () => {
      editorRef.current?.setContent('');
      germanEditorRef.current?.setContent('');
    },
      categories
  });

  const handleSelectChange = (id: string, value: SelectedValue | undefined) => {
    formik.setFieldValue(id, value);
  };

  if (categoryLoading || !categories || detailsLoading) {
    return <CircularProgress />;
  }

  return (
    <Paper elevation={3} sx={{ py: 4, width: { xs: '100%', md: '75%' } }}>
      <Typography variant="h5" fontWeight={600} sx={{ fontSize: { xs: '16px', sm: '18px' } }}>
        Add Post
      </Typography>
      <Stack onSubmit={formik.handleSubmit} component="form" direction="column" gap={2} mt={4}>
        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="title"
          >
            Title
          </InputLabel>
          <TextField
            id="title"
            type="text"
            variant="filled"
            placeholder="Title"
            autoComplete="title"
            fullWidth
            required
            error={!!formik.errors.title && formik.touched.title}
            helperText={formik.errors.title && formik.touched.title ? formik.errors.title : ''}
            {...formik.getFieldProps('title')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="summary"
          >
            Summary & Description (Meta Tag)
          </InputLabel>
          <TextField
            id="summary"
            type="text"
            variant="filled"
            placeholder="Summary"
            autoComplete="summary"
            fullWidth
            {...formik.getFieldProps('summary')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="keywords"
          >
            Keywords (Meta Tag)
          </InputLabel>
          <TextField
            id="keywords"
            type="text"
            variant="filled"
            placeholder="Keywords"
            autoComplete="keywords"
            fullWidth
            {...formik.getFieldProps('keywords')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="keywords"
          >
            Language
          </InputLabel>
          <SelectMenu
            id="language"
            value={formik.values.language}
            handleChange={handleSelectChange}
            options={languages}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="category"
          >
            Category
          </InputLabel>
          <SelectMenu
            id="category"
            value={formik.values.category}
            handleChange={handleSelectChange}
            options={[
              { _id: null, name: '--Choose--' },
              ...categories.map((item) => ({ _id: item._id, name: item.categoryName })),
            ]}
          />
          {formik.errors.category && (
            <Typography
              variant="h6"
              color="red"
              fontSize={12}
              fontWeight={400}
              fontFamily={fontFamily.workSans}
            >
              {formik.errors.category._id}
            </Typography>
          )}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="category"
          >
            Tags
          </InputLabel>
          <ChipInput chips={formik.values.tags} setChips={(v) => formik.setFieldValue('tags', v)} />
        </Box>

        <Box>
          {formik.isSubmitting ? (
            <CircularProgress />
          ) : (
            <SingleFileInput
              label="Cover Image"
              existingFile={details?.image}
              getFile={(file) => formik.setFieldValue('image', file)}
            />
          )}
          {formik.errors.image && (
            <Typography
              variant="h6"
              color="red"
              fontSize={12}
              fontWeight={400}
              fontFamily={fontFamily.workSans}
            >
              {formik.errors.image}
            </Typography>
          )}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="details.description"
          >
            Description
          </InputLabel>
          <Editor
            onInit={(_evt, editor) => (editorRef.current = editor)}
            onChange={() =>
              formik.setFieldValue('details.description', editorRef.current?.getContent())
            }
            initialValue={formik.values.details.description}
            {...editorProps}
          />
        </Box>

        <Accordion sx={{ paddingX: 0 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography sx={{ paddingX: 0 }}>German Details</Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ paddingX: 0 }}>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.germanDetails.title"
              >
                Title
              </InputLabel>
              <TextField
                id="details.germanDetails.title"
                type="text"
                variant="filled"
                placeholder="Title"
                autoComplete="details.germanDetails.title"
                fullWidth
                {...formik.getFieldProps('details.germanDetails.title')}
              />
            </Box>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.germanDetails.summary"
              >
                Summary & Description (Meta Tag)
              </InputLabel>
              <TextField
                id="details.germanDetails.summary"
                type="text"
                variant="filled"
                placeholder="Summary"
                autoComplete="details.germanDetails.summary"
                fullWidth
                {...formik.getFieldProps('details.germanDetails.summary')}
              />
            </Box>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.germanDetails.keywords"
              >
                Keywords (Meta Tag)
              </InputLabel>
              <TextField
                id="details.germanDetails.keywords"
                type="text"
                variant="filled"
                placeholder="Keywords"
                autoComplete="details.germanDetails.keywords"
                fullWidth
                {...formik.getFieldProps('details.germanDetails.keywords')}
              />
            </Box>

            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.tags"
              >
                Tags
              </InputLabel>
              <ChipInput chips={formik.values.details.germanDetails.tags} setChips={(v) => formik.setFieldValue('details.germanDetails.tags', v)} />
            </Box>
            <Box>
              {formik.isSubmitting ? (
                <CircularProgress />
              ) : (
                <SingleFileInput
                  label="Cover Image"
                  getFile={(file) => formik.setFieldValue('details.germanDetails.image', file)}
                />
              )}
              {formik.errors.image && (
                <Typography
                  variant="h6"
                  color="red"
                  fontSize={12}
                  fontWeight={400}
                  fontFamily={fontFamily.workSans}
                >
                  {formik.errors.image}
                </Typography>
              )}
            </Box>
            <Box>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="details.germanDetails.description"
              >
                Content
              </InputLabel>
              <Editor
                onInit={(_evt, editor) => (germanEditorRef.current = editor)}
                onChange={() =>
                  formik.setFieldValue(
                    'details.germanDetails.description',
                    germanEditorRef.current?.getContent(),
                  )
                }
                initialValue={formik.values.details.germanDetails.description}
                {...editorProps}
              />
            </Box>
          </AccordionDetails>
        </Accordion>

        <Button
          type="submit"
          variant="contained"
          size="medium"
          fullWidth
          disabled={formik.isSubmitting}
        >
          {formik.isSubmitting ? 'Processing...' : 'Submit'}
        </Button>
      </Stack>
    </Paper>
  );
}