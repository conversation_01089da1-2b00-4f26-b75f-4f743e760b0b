import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getBlogs } from 'pages/blogs/api/getBlogs.ts';

export type BlogPropsDto = {
  language: string;
}

export default function useBlogs(props:BlogPropsDto) {
  const { data: blogs, loading: blogsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['blogs', {...props}],
      queryFn: () => getBlogs(props),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { blogs, blogsLoading };
}
