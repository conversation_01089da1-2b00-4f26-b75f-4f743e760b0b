import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getBlogDetails from 'pages/blogs/api/getBlogDetails.ts';

export type BlogPropsDto = {
  id: string;
};

export default function useBlogDetails(props:BlogPropsDto) {
    const { data: details, loading: detailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog-details', {...props}],
            queryFn: () => getBlogDetails(props),
            onError: showError,
        }),
        transform: (data) => data.data,
    });
    return { details, detailsLoading };
}
