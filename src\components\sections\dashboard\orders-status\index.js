import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import { useState } from 'react';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import DateSelect from 'components/dates/DateSelect';
import IconifyIcon from 'components/base/IconifyIcon';
import OrdersStatusTable from './OrdersStatusTable';
const OrdersStatus = () => {
    const [searchText, setSearchText] = useState('');
    const handleInputChange = (e) => {
        setSearchText(e.target.value);
    };
    return (_jsxs(Paper, { sx: { px: 0 }, children: [_jsxs(Stack, { px: 3.5, spacing: 1.5, alignItems: { xs: 'flex-start', md: 'center' }, justifyContent: "space-between", children: [_jsxs(Stack, { spacing: 2, direction: { xs: 'column', md: 'row' }, alignItems: { xs: 'flex-start', md: 'center' }, justifyContent: "space-between", flexGrow: 1, children: [_jsx(Typography, { variant: "h6", fontWeight: 400, fontFamily: fontFamily.workSans, children: "Orders Status" }), _jsx(TextField, { variant: "filled", size: "small", placeholder: "Search for...", value: searchText, onChange: handleInputChange, sx: { width: 220 }, InputProps: {
                                    startAdornment: (_jsx(InputAdornment, { position: "start", children: _jsx(IconifyIcon, { icon: 'mingcute:search-line' }) })),
                                } })] }), _jsxs(Stack, { spacing: 1.5, direction: { xs: 'column-reverse', sm: 'row' }, alignItems: { xs: 'flex-end', sm: 'center' }, children: [_jsx(DateSelect, {}), _jsx(Button, { variant: "contained", size: "small", children: "Create order" })] })] }), _jsx(Box, { mt: 1.5, sx: { height: 594, width: 1 }, children: _jsx(OrdersStatusTable, { searchText: searchText }) })] }));
};
export default OrdersStatus;
