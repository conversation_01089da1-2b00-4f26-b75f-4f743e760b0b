export interface UserDto {
  sellerStats: SellerStats;
  _id: string;
  name: string;
  email: string;
  avatar: string;
  paymentId: string;
  ipAddress: string;
  loginWithFacebook: boolean;
  loginWithGoogle: boolean;
  currentOrder: string | null;
  country: string;
  phoneNumber: string;
  role: 'seller' | 'buyer' | string;
  enable2FA: boolean;
  address: Address[];
  createdAt: string;
  updatedAt: string;
  lastActiveAt: string;
  __v: number;
}

export interface SellerStats {
  totalOrders: number;
  completedOrders: number;
  averageRating: number;
  tier: string;
  completionRate: string;
  totalSales: number;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  _id?: string;
}
