import * as Yup from 'yup';
import { useMutation, useQueryClient } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { postBlogCategory } from 'pages/blogs/api/post-blog-category.ts';
import { SelectedValue } from 'components/select-menu.tsx';
import { languages } from 'pages/category/const/languages.ts';

export type ErrorType = {
  message: string;
};

const blogCategorySchema = Yup.object().shape({
    language: Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string().optional(),
    }),
    categoryName: Yup.string().required('Category Name is required'),
    description: Yup.string().required('Description is required'),
    keywords: Yup.string().required('Meta Keywords are required'),
    order: Yup.number().required('Order is required'),
});

type InitialValuesDto = {
    language: SelectedValue;
    categoryName: string;
    description: string;
    keywords: string;
    order: number;
};

export default function useBlogCategoryForm() {
    const queryClient = useQueryClient();
    const initialValues = <InitialValuesDto>({
        language: languages[1],
        categoryName: "",
        description: "",
        keywords: "",
        order: 1,
    });

    const { mutateAsync } = useMutation(postBlogCategory, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: blogCategorySchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({ ...values, language: values.language.name, keywords: values.keywords.split(",") });
            setSubmitting(false);
            resetForm();
            if (response) {
                queryClient.invalidateQueries("blog-categories").finally()
                showSuccess("Category Added Successfully.")
            }
        },
    });

    return { formik };
}
