import { patchRequest } from 'vbrae-utils';
import {InitialValuesDto} from "pages/faq/hooks/useFaqForm.ts";

interface Response {
    status: string;
}

interface ExtendedInitialValuesDto extends InitialValuesDto {
    _id: string;
}

export async function patchFaq({ _id, ...otherProps }: ExtendedInitialValuesDto): Promise<Response> {
    const r = await patchRequest({
        url: `faqs/${_id}`,
        data: otherProps,
        useAuth: true,
    });

    return r.response;
}
