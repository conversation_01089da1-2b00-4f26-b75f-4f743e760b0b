import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import Button from '@mui/material/Button';
import useLevels from 'pages/levels/hooks/useLevels.ts';
import useResetLevels from 'pages/levels/hooks/useResetLevels.ts';
import { LevelDto } from 'pages/levels/types/level.ts';
import LevelModal from 'pages/levels/components/LevelModal.tsx';

type LevelModalState = {
  isOpen: boolean;
  state: LevelDto;
};

const initialState : LevelModalState = {
  isOpen: false,
  state: {
    _id: '',
    name: '',
    order: 0,
    fromOrders: 0,
    toOrders: 0,
    earningReleaseDays: 0,
    commissionPercent: 0,
  },
};

export default function LevelView() {
  const [searchValue, setSearchValue] = useState('');
  const { levels, } = useLevels();
  const [levelToUpdate, setLevelToUpdate] = useState<LevelModalState>(initialState);
  const { resetLoading, resetRefetch } = useResetLevels();

  const columns = useMemo<ColumnDef<LevelDto>[]>(
    () => [
      {
        header: 'Title',
        accessorKey: 'name',
        cell: (info) => info.getValue(),
      },
      {
        header: 'From Orders',
        accessorKey: 'fromOrders',
        cell: (info) => info.getValue(),
      },
      {
        header: 'To Orders',
        accessorKey: 'toOrders',
        cell: (info) => (info.getValue() === null ? '∞' : info.getValue()),
      },
      {
        header: 'Earning Release Days',
        accessorKey: 'earningReleaseDays',
        cell: (info) => info.getValue(),
      },
      {
        header: 'Commission %',
        accessorKey: 'commissionPercent',
        cell: (info) => info.getValue() + '%',
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Edit',
              action: () => setLevelToUpdate({ isOpen: true, state: info.row.original }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (!levels) return <CircularProgress />;

  return (
    <>
      <LevelModal
        open={levelToUpdate.isOpen}
        state={levelToUpdate.state}
        handleClose={() => setLevelToUpdate(initialState)}
      />
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Levels List
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Button type="submit" variant="contained" size="small" onClick={resetRefetch} disabled={resetLoading}>
          Reset Levels
        </Button>
        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={levels}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}
