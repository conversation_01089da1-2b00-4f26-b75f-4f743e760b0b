import { Box, Button, DialogActions, InputLabel, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import Stack from '@mui/material/Stack';
import useLevelForm from 'pages/levels/hooks/useLevelForm.ts';
import { LevelDto } from 'pages/levels/types/level.ts';

type LevelModalProps = {
  open: boolean;
  handleClose: () => void;
  state: LevelDto;
};

const LevelModal = ({ open, handleClose, state }: LevelModalProps) => {
  const { formik } = useLevelForm({ existingLevel: state, handleClose });

  return (
    <Dialog
      open={open}
      fullWidth
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Stack direction="row" justifyContent="space-between">
        <DialogTitle sx={{ padding: 0 }} id="alert-dialog-title">
          {state._id ? 'Update' : 'Add'} Level
        </DialogTitle>
        <DialogActions>
          <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
        </DialogActions>
      </Stack>
      <DialogContent sx={{ padding: 0 }}>
        <form onSubmit={formik.handleSubmit}>
          <Box sx={{ marginBottom: '20px' }}>
            <InputLabel sx={{ fontSize: '12px', marginBottom: '5px' }}>From Orders</InputLabel>
            <TextField
              type="number"
              fullWidth
              variant="filled"
              placeholder="From Orders"
              {...formik.getFieldProps('fromOrders')}
              error={formik.touched.fromOrders && Boolean(formik.errors.fromOrders)}
              helperText={formik.touched.fromOrders && formik.errors.fromOrders}
            />
          </Box>

          <Box sx={{ marginBottom: '20px' }}>
            <InputLabel sx={{ fontSize: '12px', marginBottom: '5px' }}>To Orders</InputLabel>
            <TextField
              type="number"
              fullWidth
              variant="filled"
              placeholder="To Orders"
              {...formik.getFieldProps('toOrders')}
              error={formik.touched.toOrders && Boolean(formik.errors.toOrders)}
              helperText={formik.touched.toOrders && formik.errors.toOrders}
            />
          </Box>

          <Box sx={{ marginBottom: '20px' }}>
            <InputLabel sx={{ fontSize: '12px', marginBottom: '5px' }}>Earning Release Days</InputLabel>
            <TextField
              type="number"
              fullWidth
              variant="filled"
              placeholder="Earning Release Days"
              {...formik.getFieldProps('earningReleaseDays')}
              error={formik.touched.earningReleaseDays && Boolean(formik.errors.earningReleaseDays)}
              helperText={formik.touched.earningReleaseDays && formik.errors.earningReleaseDays}
            />
          </Box>

          <Box sx={{ marginBottom: '20px' }}>
            <InputLabel sx={{ fontSize: '12px', marginBottom: '5px' }}>Commission Percent</InputLabel>
            <TextField
              type="number"
              fullWidth
              variant="filled"
              placeholder="Commission Percent"
              {...formik.getFieldProps('commissionPercent')}
              error={formik.touched.commissionPercent && Boolean(formik.errors.commissionPercent)}
              helperText={formik.touched.commissionPercent && formik.errors.commissionPercent}
            />
          </Box>

          <Button
            type="submit"
            variant="contained"
            size="medium"
            sx={{ marginTop: '20px', width: '20%', marginLeft: 'auto' }}
          >
            {formik.isSubmitting ? 'Processing...' : 'Submit'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default LevelModal;
