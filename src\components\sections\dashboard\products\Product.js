import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Image from 'components/base/Image';
const Product = ({ data }) => {
    const { imageUrl, name, inStock, price } = data;
    return (_jsxs(Stack, { alignItems: "center", justifyContent: "space-between", children: [_jsxs(Stack, { spacing: 2, alignItems: "center", children: [_jsx(Box, { height: 46, width: 46, bgcolor: "info.dark", borderRadius: 1.25, children: _jsx(Image, { src: imageUrl, height: 1, width: 1, sx: { objectFit: 'contain' } }) }), _jsxs(Stack, { direction: "column", children: [_jsx(Typography, { variant: "body2", fontWeight: 600, children: name }), _jsxs(Typography, { variant: "caption", color: "text.secondary", fontWeight: 500, children: [inStock, " in stock"] })] })] }), _jsxs(Typography, { variant: "caption", fontWeight: 400, fontFamily: fontFamily.workSans, children: ["$ ", price] })] }));
};
export default Product;
