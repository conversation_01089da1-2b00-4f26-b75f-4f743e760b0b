import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { useEffect, useState } from 'react';
import { BlogPost } from 'pages/blogs/types/blog.ts';
import { postBlogForm } from 'pages/blogs/api/post-blog-form.ts';
import { patchBlogForm } from 'pages/blogs/api/patch-blog-form.ts';
import {uploadUrl} from "pages/category/api/uploadUrl.ts";
import {languages} from "pages/category/const/languages.ts";
import {SelectedValue} from "components/select-menu.tsx";
import {BlogCategory} from "pages/blogs/types/blog-category.ts";

interface ErrorType {
  message: string;
}

interface BlogDetailsProps {
    blogDetails?: BlogPost;
    onSubmit: () => void;
    categories?: BlogCategory[];
}

const blogSchema = Yup.object().shape({
    title: Yup.string()
        .trim()
        .required('Title is required'),
    summary: Yup.string().optional(),
    keywords: Yup.string().optional(),
    language: Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string().optional(),
    }),
    category: Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string().optional(),
    }),
    tags: Yup.array().of(Yup.string()),
    details: Yup.object().shape({
        description: Yup.string().optional(),
        germanDetails: Yup.object().shape({
            description: Yup.string().optional(),
        }),
        frenchDetails: Yup.object().shape({
            description: Yup.string().optional(),
        }),
    }),
});

type InitialStateDto = {
    title: string,
    summary: string,
    keywords: string,
    language: SelectedValue,
    category: SelectedValue,
    tags: string[],
    image: string,
    details: {
        description: string,
        germanDetails: { description: string, image: string, tags: string[], summary: string, keywords:string, title: string},
        frenchDetails: { description: string, image: string, tags: string[], summary: string, keywords:string, title: string},
    },
}

export default function useBlogForm({ blogDetails, onSubmit, categories }: BlogDetailsProps) {
    const [initialValues, setInitialValues] = useState<InitialStateDto>({
        title: '',
        summary: '',
        keywords: '',
        language: { _id: null, name: "--Choose--" },
        category: { _id: null, name: "--Choose--" },
        tags: [],
        image: '',
        details: {
            description: '',
            germanDetails: { description: '', image: '', tags:[], summary:'', keywords:'', title:''},
            frenchDetails: { description: '', image: '', tags:[], summary:'', keywords:'', title:''},
        },
    });

    useEffect(() => {
        if (!blogDetails || !categories) return;

        const category = categories
            .map(({ _id, categoryName }) => ({ _id, name: categoryName }))
            .find(cat => cat._id === blogDetails.category._id) ?? { _id: null, name: "--Choose--" };

        setInitialValues({
            title: blogDetails.title,
            summary: blogDetails.summary,
            keywords: blogDetails.keywords.join(","),
            language: languages.find(lang=> lang._id === blogDetails.language) ?? { _id: null, name: "--Choose--" },
            category,
            image: blogDetails.image ?? '',
            tags: blogDetails.tags ?? [],
            details: {
                description: blogDetails.details?.description ?? '',
                germanDetails: {
                    description: blogDetails.details?.germanDetails?.description ?? '',
                    image: blogDetails.details?.germanDetails?.image ?? '',
                    tags: blogDetails.details?.germanDetails?.tags ?? [],
                    summary: blogDetails.details?.germanDetails?.summary ?? '',
                    keywords: blogDetails.details?.germanDetails?.keywords.join(","),
                    title: blogDetails.details?.germanDetails?.title ?? '',
                },
                frenchDetails: {
                    description: '', image: '', tags:[], summary:'', keywords:'', title:''
                },
            },
        });
    }, [blogDetails, categories]);

    const { mutateAsync: updateAsync } = useMutation(patchBlogForm, {
        onError: (error: ErrorType) => showError(error),
    });

    const { mutateAsync } = useMutation(postBlogForm, {
        onError: (error: ErrorType) => showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: blogSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;

            // Function to upload an image if it's a file
            const uploadImage = async (image: string | File) => {
                if (image instanceof File) {
                    const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
                    const resignedResponse = await uploadUrl({ name: image.name, fileType });
                    const { url: resignedUrl, path: filePath } = resignedResponse;

                    await fetch(resignedUrl, {
                        method: 'PUT',
                        headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                        body: image,
                    });

                    return filePath;
                }
                return image; // Return the existing URL if not a File
            };

            // Upload cover images
            const image = await uploadImage(values.image);
            const germanImage = await uploadImage(values.details.germanDetails.image);
            const frenchImage = await uploadImage(values.details.frenchDetails.image);

            const payload = {
                ...values,
                image,
                category: values.category._id as string,
                language: values.language._id as string,
                keywords: values.keywords.length > 0 ? values.keywords.split(",") : [],
                details: {
                    ...values.details,
                    germanDetails: { ...values.details.germanDetails, image: germanImage },
                    frenchDetails: { ...values.details.frenchDetails, image: frenchImage },
                },
            };

            if (!blogDetails) {
                response = await mutateAsync(payload);
            } else {
                response = await updateAsync({ ...payload, _id: blogDetails._id });
            }

            setSubmitting(false);
            resetForm();
            if (response) {
                onSubmit?.();
                showSuccess(response.message);
            }
        },
    });

    return { formik };
}
