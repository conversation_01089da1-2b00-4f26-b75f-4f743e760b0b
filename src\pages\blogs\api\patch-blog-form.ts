import { patchRequest } from 'vbrae-utils';

type BlogPostDto = {
  _id: string;
  image: string;
  category: string;
  language: string;
  keywords: string[];
  details: {
    germanDetails: {
        image: string;
      description: string;
    };
    frenchDetails: {
        image: string;
      description: string;
    };
    description: string;
  };
  title: string;
  summary: string;
  tags: string[];
};

export async function patchBlogForm(props: BlogPostDto): Promise<{message: string}> {
    const r = await patchRequest<BlogPostDto>({
        url: `blog/${props._id}`,
        data: props,
        useAuth: true,
    });
    return r.response;
}