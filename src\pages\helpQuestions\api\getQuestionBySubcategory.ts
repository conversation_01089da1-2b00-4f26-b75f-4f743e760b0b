import { getRequest } from 'vbrae-utils';
import { AccordionQuestionDto } from 'pages/category/types/accordion.ts';

type APIResponse = {
  subcategoryTitle: string;
  questions: AccordionQuestionDto[];
};

type PropDto = {
  id: string;
  subCategoryId: string;
}

export async function getQuestionBySubcategory({ id, subCategoryId }: PropDto): Promise<APIResponse> {
  return await getRequest({
    url: `help/${id}/subcategories/${subCategoryId}`,
    useAuth: true,
  });
}
