import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import Link from '@mui/material/Link';
import List from '@mui/material/List';
import Collapse from '@mui/material/Collapse';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import IconifyIcon from 'components/base/IconifyIcon';
const CollapseListItem = ({ subheader, active, items, icon }) => {
    const [open, setOpen] = useState(false);
    const handleClick = () => {
        setOpen(!open);
    };
    return (_jsxs(_Fragment, { children: [_jsxs(ListItemButton, { onClick: handleClick, children: [_jsx(ListItemIcon, { children: icon && (_jsx(IconifyIcon, { icon: icon, sx: {
                                color: active ? 'primary.main' : null,
                            } })) }), _jsx(ListItemText, { primary: subheader, sx: {
                            '& .MuiListItemText-primary': {
                                color: active ? 'primary.main' : null,
                            },
                        } }), _jsx(IconifyIcon, { icon: "iconamoon:arrow-right-2-duotone", color: "neutral.dark", sx: {
                            transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                            transition: 'transform 0.2s ease-in-out',
                        } })] }), _jsx(Collapse, { in: open, timeout: "auto", unmountOnExit: true, children: _jsx(List, { component: "div", disablePadding: true, children: items?.map((route) => {
                        return (_jsx(ListItemButton, { component: Link, href: route.path, sx: {
                                pl: 1.75,
                                borderLeft: 4,
                                borderStyle: 'solid',
                                borderColor: route.active ? 'primary.main' : 'transparent !important',
                                bgcolor: route.active ? 'info.dark' : 'info.darker',
                            }, children: _jsx(ListItemText, { primary: route.name, sx: {
                                    '& .MuiListItemText-primary': {
                                        color: route.active ? 'text.primary' : 'text.secondary',
                                    },
                                } }) }, route.pathName));
                    }) }) })] }));
};
export default CollapseListItem;
