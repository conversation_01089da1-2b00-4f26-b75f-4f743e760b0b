import * as React from 'react';
import { Box, Button, DialogActions, InputLabel, Switch, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import Stack from '@mui/material/Stack';
import FormControlLabel from '@mui/material/FormControlLabel';
import useSubCategoryForm from 'pages/helpQuestions/hooks/useSubCategoryForm.ts';

type QuestionModalProps = {
    open: boolean;
    handleClose: ()=> void;
    state: {
        _id: string,
        title?: string,
    }
}

const QuestionSubCategoryModal = ({ open, handleClose, state } : QuestionModalProps) => {
    const { formik } = useSubCategoryForm({ existingSubCategory: state, handleClose });

    return (
        <React.Fragment>
            <Dialog
                open={open}
                fullWidth={true}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <Stack direction="row" justifyContent="space-between">
                    <DialogTitle sx={{ padding: 0 }} id="alert-dialog-title">
                        {state.title ? "Update" : "Add"} Category
                    </DialogTitle>
                    <DialogActions>
                        <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
                    </DialogActions>
                </Stack>
                <DialogContent sx={{ padding: 0 }}>
                    <form onSubmit={formik.handleSubmit}>
                        <Box sx={{ marginBottom: '20px' }}>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="title"
                            >
                                Title
                            </InputLabel>
                            <TextField
                                id="title"
                                type="text"
                                variant="filled"
                                placeholder="Title"
                                autoComplete="title"
                                fullWidth
                                required
                                {...formik.getFieldProps('title')}
                            />
                        </Box>

                        <Box>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formik.values.isActive}
                                        onChange={(e) => formik.setFieldValue('isActive', e.target.checked)}
                                        color="primary"
                                    />
                                }
                                label="Is Active"
                                sx={{ fontSize: '12px' }}
                            />
                        </Box>
                        <Button
                            type="submit"
                            variant="contained"
                            size="medium"
                            sx={{ marginTop: '20px', width: '20%', marginLeft: 'auto' }}
                        >
                            Submit
                        </Button>
                    </form>
                </DialogContent>
            </Dialog>
        </React.Fragment>
    );
};

export default QuestionSubCategoryModal;
