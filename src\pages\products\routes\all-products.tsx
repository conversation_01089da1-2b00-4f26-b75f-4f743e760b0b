import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { InputBase, InputLabel } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import useAllProducts from 'pages/products/hooks/useAllProducts.ts';
import { Product } from 'pages/products/types/product.ts';
import Avatar from '@mui/material/Avatar';
import BasicDropdown from 'components/dropdown.tsx';
import FeeModal from 'components/modals/FeeModal.tsx';
import useUpdateProduct from 'pages/products/hooks/useUpdateProduct.ts';
import useDeleteProduct from 'pages/products/hooks/useDeleteProduct.ts';
import useCategories from 'pages/category/hooks/useCategories.ts';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import { stockOptions } from 'pages/products/constant/stockOptions.ts';
import { generateQueryString } from 'pages/products/functions/productFunctions.ts';
import {useNavigate} from "react-router-dom";

export interface UpdateProps {
  [key: string]: string | boolean;
}

export default function AllProducts() {
  const { categories } = useCategories({});

  const navigate = useNavigate();

  const [searchValue, setSearchValue] = useState('');
  const [subCategoryOption, setSubCategoryOption] = useState<SelectedValue[]>([]);
  const [open, setOpen] = useState({value: false, _id: "", price: '1'});
  const [productToUpdate, setProductToUpdate] = useState<UpdateProps>({_id: ""});
  const [selectedCategory, setSelectedCategory] = useState<SelectedValue>({_id: null, name: "All"})
  const [selectedSubCategory, setSelectedSubCategory] = useState<SelectedValue>({_id: null, name: "All"})
  const [selectedStock, setSelectedStock] = useState<SelectedValue>({_id: null, name: "All"})
  const [productToDelete, setProductToDelete] = useState({_id: ""});

  const { products, productLoading } = useAllProducts({query: generateQueryString(selectedCategory, selectedSubCategory, selectedStock, `active=true`)});
  const {updateRefetch} = useUpdateProduct(productToUpdate);
  const {deleteRefetch} = useDeleteProduct(productToDelete);

  useEffect(() => {
      if(!productToUpdate._id) return;

      updateRefetch().finally(()=> setProductToUpdate({_id: ""}))
  }, [productToUpdate]);

  useEffect(() => {
    if(!productToDelete._id) return;

      deleteRefetch().finally(()=> setProductToDelete({_id: ""}))
  }, [productToDelete])

  const columns = useMemo<ColumnDef<Product>[]>(
    () => [
      {
        header: 'Product',
        accessorFn: (row) => row.template?.templateName,
        id: 'productName',
        cell: (info) => (
            <Box display="flex" alignItems="center" gap={2}>
                <Avatar
                    src={info.row.original.template?.coverImage || ''}
                    alt={info.row.original.template?.templateName || 'Product Image'}
                    sx={{ width: 50, height: 50 }}
                />
                <Typography variant="body1" noWrap>
                    {info.row.original.template?.templateName || 'N/A'}
                </Typography>
            </Box>
        ),
        enableGlobalFilter: true,
      },
      {
        header: 'Category',
        accessorFn: (row) => row.template?.category?.categoryName.en,
        id: 'category',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'User',
        accessorFn: (row) => row.seller.name,
        id: 'name',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Stock',
        accessorFn: (row) => row.stock,
        id: 'stock',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Date',
        accessorFn: (row) => row.createdAt,
        id: 'createdAt',
          cell: (info) => <span>{info.row.original.createdAt.split("T")[0]}</span>,
        enableGlobalFilter: true,
      },
        {
            header: 'Actions',
            accessorFn: () => {},
            id: 'actions',
            cell: (info) => {
                const options = [
                    {
                        title: "Add Fee",
                        action: ()=> setOpen({_id: info.row.original._id, value: true, price: String(info.row.original.expectedPrice)})
                    },
                    {
                        title: "Edit Product",
                        action: ()=> navigate(`/edit-product/${info.row.original._id}`)
                    },
                    {
                        title: "Delete Permanently",
                        action: ()=> setProductToDelete({_id: info.row.original._id})
                    }
                ]
                if(!info.row.original.featured) {
                    options.push({
                        title: "Add to featured",
                        action: ()=> setProductToUpdate({_id: info.row.original._id, featured: true})
                    })
                }

                if(info.row.original.active){
                    options.push({
                    title: "Delete",
                    action: ()=> setProductToUpdate({_id: info.row.original._id, active: false})
                })
                }

                if(!info.row.original.specialOffer){
                    options.push({
                    title: "Add to Special",
                    action: ()=> setProductToUpdate({_id: info.row.original._id, specialOffer: true})
                })
                }
                return <BasicDropdown options={options} />
            },
            enableGlobalFilter: false,
        }
    ],
    [],
  );

    const handleSelectChange = (id : string, value?: SelectedValue) => {
        if(id === 'parent_category') {
            const newCategory = categories?.find((c) => c.slug === value?._id);
            const subCategory = newCategory?.children.map(item=> ({_id: item._id, name: item.categoryName.en})) ?? [];
            setSelectedCategory(value ?? {_id: "", name: ""});
            setSubCategoryOption(subCategory)
            setSelectedSubCategory({_id: null, name: "All"});
        }
        if(id === 'sub_category') setSelectedSubCategory(value ?? {_id: "", name: ""});
        if(id === 'stock') setSelectedStock(value ?? {_id: "", name: ""});
    }

  if (productLoading || !products || !categories) return <CircularProgress />;
  return (
    <Stack direction="column" spacing={2}>
      <Typography
        variant="h5"
        fontWeight={600}
        letterSpacing={1}
        fontFamily={fontFamily.workSans}
        display={{ xs: 'none', lg: 'block' }}
      >
        All Products
      </Typography>

        <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
            <Stack direction="row" gap={2} alignItems="end">
                <Box width={{xs: "100%", sm:"160px"}}>
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="parent_category"
                    >
                        Category
                    </InputLabel>
                    <SelectMenu
                        value={selectedCategory}
                        id="parent_category"
                        handleChange={handleSelectChange}
                        options={[{_id: null, name: "All"}, ...categories.map(item=>({
                            _id: item.slug,
                            name: item.categoryName.en,
                        }))]}
                    />
                </Box>

                <Box width={{xs: "100%", sm:"160px"}}>
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="sub_category"
                    >
                        Sub Category
                    </InputLabel>
                    <SelectMenu
                        value={selectedSubCategory}
                        id="sub_category"
                        handleChange={handleSelectChange}
                        options={[{_id: null, name: "All"}, ...subCategoryOption]}
                    />
                </Box>

                <Box width={{xs: "100%", sm:"160px"}}>
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="stock"
                    >
                        Stock
                    </InputLabel>
                    <SelectMenu
                        value={selectedStock}
                        id="stock"
                        handleChange={handleSelectChange}
                        options={stockOptions}
                    />
                </Box>
            </Stack>

            <Box>
                <Paper
                    component="form"
                    sx={{
                        p: '2px 4px',
                        display: 'flex',
                        alignItems: 'end',
                        justifyContent: 'center',
                        width: { xs: '100%', sm: 300 },
                    }}
                >
                    <InputBase
                        sx={{ ml: 1, flex: 1, border: 'none' }}
                        placeholder="Search here"
                        inputProps={{ 'aria-label': 'search google maps' }}
                        onChange={(e) => setSearchValue(e.target.value.trim())}
                    />
                    <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                        <SearchIcon />
                    </IconButton>
                </Paper>
            </Box>
        </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        {productLoading && <CircularProgress />}
        <TableComponent
          columns={columns}
          data={products}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
        <FeeModal open={open} setOpen={setOpen} />
    </Stack>
  );
}