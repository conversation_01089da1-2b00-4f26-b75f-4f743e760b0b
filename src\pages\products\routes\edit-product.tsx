import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { FormControl, InputLabel, Radio, RadioGroup, SelectChangeEvent } from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import Paper from '@mui/material/Paper';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import {useEffect, useState} from 'react';
import useCategories from 'pages/category/hooks/useCategories.ts';
import { regionsList } from 'pages/templates/const/regionList.ts';
import useProductDetails from 'pages/products/hooks/useProductDetails.ts';
import { useParams } from 'react-router-dom';
import useProductForm from 'pages/products/hooks/useProductForm.ts';
import SingleFileInput from 'components/single-file-input.tsx';

export default function EditProduct() {
  const { id = '' } = useParams();

  const { categories } = useCategories({});
  const { product } = useProductDetails({ _id: id });

  const { formik } = useProductForm({ product });

  const [subCategoryOption, setSubCategoryOption] = useState<SelectedValue[]>([]);

  const handleChange = (event: SelectChangeEvent<string | number>, id: string) => {
    formik.setFieldValue(id, event.target.value);
  };

  useEffect(() => {
    if(!categories) return;

    const newCategory = categories?.find((c) => c.slug === formik.values.category._id);
    const subCategory = newCategory?.children.map((item) => ({ _id: item.slug, name: item.slug })) ?? [];
    setSubCategoryOption(subCategory);

  }, [formik.values.category, categories]);

  const handleSelectChange = (id: string, value?: SelectedValue) => {
    formik.setFieldValue(id, value ?? { _id: '', name: '' });
    if (id === 'category') {
      const newCategory = categories?.find((c) => c.slug === value?._id);
      const subCategory = newCategory?.children.map((item) => ({ _id: item.slug, name: item.slug })) ?? [];
      setSubCategoryOption(subCategory);
      formik.setFieldValue('subcategory', { _id: null, name: 'None' });
    }
    if (id === 'subcategory') formik.setFieldValue('subcategory', value ?? { _id: null, name: 'None' });
  };

  if (!product) {
    return <CircularProgress />;
  }

  return (
    <Paper elevation={3} sx={{ py: 4, width: { xs: '100%', md: '75%' } }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
        <Typography variant="h5" fontWeight={600} sx={{ fontSize: { xs: '16px', sm: '18px' } }}>
          Edit Product
        </Typography>
      </Stack>
      <Stack onSubmit={formik.handleSubmit} component="form" direction="column" gap={2} mt={4}>
        <Box>
          {formik.isSubmitting ? (
            <CircularProgress />
          ) : (
            <SingleFileInput
              label="Cover Images"
              getFile={(file) => formik.setFieldValue('coverImage', file)}
              existingFile={product.template?.coverImage}
            />
          )}
        </Box>

        <Stack direction="column">
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
            size="small"
            htmlFor="instantDelivery"
          >
            Listing Type
          </InputLabel>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              {...formik.getFieldProps('instantDelivery')}
              onChange={(e) => handleChange(e, 'instantDelivery')}
            >
              <FormControlLabel value="sale" control={<Radio />} label="Add a product for sale" />
              <FormControlLabel
                value={false}
                control={<Radio />}
                label="Add a product to receive quote requests"
              />
              <FormControlLabel
                value={true}
                control={<Radio />}
                label="Add a product to sell licence keys"
              />
            </RadioGroup>
          </FormControl>
        </Stack>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="category"
          >
            Category
          </InputLabel>
          {categories && (
            <SelectMenu
              value={formik.values.category}
              id="category"
              handleChange={handleSelectChange}
              options={[
                { _id: null, name: 'None' },
                ...categories.map((item) => ({
                  _id: item.slug,
                  name: item.slug,
                })),
              ]}
            />
          )}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="subcategory"
          >
            Sub Category
          </InputLabel>
          <SelectMenu
            value={formik.values.subcategory}
            id="subcategory"
            handleChange={handleSelectChange}
            options={[{ _id: null, name: 'None' }, ...subCategoryOption]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="region"
          >
            Region
          </InputLabel>
          <SelectMenu
            id="region"
            value={formik.values.region}
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...regionsList]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="genres"
          >
            Genre
          </InputLabel>
          <TextField
            id="genres"
            type="text"
            variant="filled"
            placeholder="Genres"
            autoComplete="genres"
            fullWidth
            autoFocus
            required
            {...formik.getFieldProps('genres')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="slug"
          >
            Slug
          </InputLabel>
          <TextField
            id="slug"
            type="text"
            variant="filled"
            placeholder="Slug"
            autoComplete="slug"
            fullWidth
            required
            {...formik.getFieldProps('slug')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="active"
          >
            Status
          </InputLabel>
          <SelectMenu
            id="active"
            value={formik.values.active}
            handleChange={handleSelectChange}
            options={[
              { _id: true, name: 'Active' },
              { _id: false, name: 'Sold' },
            ]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="visibility"
          >
            Visibility
          </InputLabel>
          <SelectMenu
            id="visibility"
            value={formik.values.visibility}
            handleChange={handleSelectChange}
            options={[
              { _id: true, name: 'Visible' },
              { _id: false, name: 'Hidden' },
            ]}
          />
        </Box>

        <Button
          type="submit"
          variant="contained"
          size="medium"
          fullWidth
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Submit
        </Button>
      </Stack>
    </Paper>
  );
}