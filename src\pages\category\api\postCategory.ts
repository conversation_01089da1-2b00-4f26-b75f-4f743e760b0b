import {postRequest} from "vbrae-utils";

interface CategoryFormValues {
    categoryName: {
        en: string;
        fr: string;
        it: string;
        de: string;
        es: string;
    };
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    category_order: number;
    visibility: boolean;
    showInMainMenu: boolean;
    showImageOnMainMenu: boolean;
    image: string | null;
    parent_id: string | null | boolean;
}


export async function postCategory(props: CategoryFormValues): Promise<{message: string} | undefined> {
    const r = await postRequest<CategoryFormValues>({
        url: 'sub-catagory',
        data : props,
        useAuth: true
    });
    return r.response
}