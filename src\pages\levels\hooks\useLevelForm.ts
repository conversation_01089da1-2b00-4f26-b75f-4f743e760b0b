import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { showError } from 'vbrae-utils';
import { ErrorType } from 'pages/authentication/hooks/useLoginForm';
import * as Yup from 'yup';
import { LevelDto } from 'pages/levels/types/level.ts';
import { patchLevel } from 'pages/levels/api/patchLevel.ts';

type FormProps = {
  existingLevel: LevelDto;
  handleClose: () => void;
};

const levelSchema = Yup.object().shape({
  _id: Yup.string().optional(),
  fromOrders: Yup.number().required('Required'),
  toOrders: Yup.number().nullable(),
  earningReleaseDays: Yup.number().required('Required'),
  commissionPercent: Yup.number().required('Required'),
});

export type LevelFormValues = Yup.InferType<typeof levelSchema>;

export default function useTaxForm({ existingLevel, handleClose }: FormProps) {
  const [initialValues, setInitialState] = useState<LevelFormValues>({
    fromOrders: 0,
    toOrders: null,
    earningReleaseDays: 0,
    commissionPercent: 0,
  });

  useEffect(() => {
    if (!existingLevel?._id) return;

    setInitialState({
      fromOrders: existingLevel.fromOrders,
      toOrders: existingLevel.toOrders ?? null,
      earningReleaseDays: existingLevel.earningReleaseDays,
      commissionPercent: existingLevel.commissionPercent,
    });
  }, [existingLevel]);

  const { mutateAsync } = useMutation(patchLevel, {
    onError: (error: ErrorType) => showError(error),
  });

  const queryClient = useQueryClient();

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: levelSchema,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const response = await mutateAsync({ ...values, _id: existingLevel._id! });
      setSubmitting(false);
      if (response) {
        queryClient.invalidateQueries('levels').finally();
        handleClose();
      }
    },
  });

  return { formik };
}
