import { jsx as _jsx } from "react/jsx-runtime";
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import { Outlet } from "react-router-dom";
import { useEffect } from "react";
import { getAccessToken } from "vbrae-utils";
import { useNavigate } from "react-router-dom";
const AuthLayout = () => {
    const navigate = useNavigate();
    useEffect(() => {
        if (getAccessToken())
            navigate("/");
    }, []);
    return (_jsx(Stack, { component: "main", alignItems: "center", justifyContent: "center", px: 1, py: 7, width: 1, minHeight: '100vh', children: _jsx(Paper, { sx: { py: 4, width: 1, maxWidth: 460 }, children: _jsx(Outlet, {}) }) }));
};
export default AuthLayout;
