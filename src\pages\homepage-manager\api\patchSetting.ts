import { patchRequest } from 'vbrae-utils';

interface SettingProps {
  topOffers: {
    enabled: boolean;
    limit: number;
  };
  softwares: {
    enabled: boolean;
    limit: number;
  };
  newArrivals: {
    enabled: boolean;
    limit: number;
  };
  articles: {
    enabled: boolean;
    limit: number;
  };
}

export async function patchSetting(props: SettingProps): Promise<{message: string} | undefined> {
    const r = await patchRequest({
        url: `homepage-config/67969b2be7d61cc63e2dc96a`,
        data : props,
        useAuth: true
    });
    return r.response
}