import { jsxs as _jsxs, Fragment as _Fragment, jsx as _jsx } from "react/jsx-runtime";
import { useMemo, useState } from 'react';
import { TableComponent } from 'components/table/table.tsx';
import Paper from '@mui/material/Paper';
import { InputBase } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import useAllRequests from 'pages/templates/hooks/useAllRequests.ts';
import Button from '@mui/material/Button';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import rejectRequest from 'pages/templates/api/rejectRequest.ts';
import approveRequest from 'pages/templates/api/approveRequest.ts';
export default function TemplateRequest() {
    const [searchValue, setSearchValue] = useState('');
    const { data, loading, refetch } = useAllRequests();
    const columns = useMemo(() => [
        {
            header: '#',
            accessorFn: (_, index) => `${index + 1}`,
            id: '_index',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Username',
            accessorFn: (row) => row.seller.name,
            id: 'title',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Title',
            accessorFn: (row) => row.title,
            id: 'title',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Regional Limit',
            accessorFn: (row) => row.region,
            id: 'region',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Platform',
            accessorFn: (row) => row.category?.categoryName?.en || '',
            id: 'category.categoryName.en',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Language',
            accessorFn: (row) => row.language.join(', '),
            id: 'language',
            cell: (info) => (_jsx(_Fragment, { children: info.row.original.language.map((item, index) => (_jsxs("span", { children: [item, index < info.row.original.language.length - 1 && ', '] }, index))) })),
        },
        {
            header: 'Date',
            accessorFn: (row) => row.createdAt,
            id: 'created_at',
            cell: (info) => _jsx("span", { children: info.row.original.createdAt.split('T')[0] }),
        },
        {
            header: 'Status',
            accessorFn: (row) => row.isApproved,
            id: 'isApproved',
            cell: (info) => info.getValue(),
        },
        {
            header: 'Action',
            accessorFn: () => { },
            id: 'action',
            cell: (info) => (_jsx(Stack, { direction: "row", spacing: 1, alignItems: "center", children: info.row.original.isApproved === 'waiting' && _jsxs(_Fragment, { children: [_jsx(Button, { onClick: () => handleActions(info.row.original._id, 'accept'), sx: { padding: 0, minWidth: 0 }, children: _jsx(IconifyIcon, { icon: "ion:checkmark-circle-outline", sx: { fontSize: '20px' } }) }), _jsx(Button, { sx: { padding: 0, minWidth: 0 }, onClick: () => handleActions(info.row.original._id, 'reject'), children: _jsx(IconifyIcon, { icon: "ion:close-outline", sx: { fontSize: '20px' }, onClick: () => { } }) })] }) })),
        },
    ], []);
    const handleActions = async (_id, type) => {
        if (type === 'reject')
            await rejectRequest({ _id });
        else
            await approveRequest({ _id });
        await refetch();
    };
    if (loading || !data) {
        return _jsx("p", { children: "Loading..." });
    }
    return (_jsxs(Stack, { direction: "column", spacing: 2, children: [_jsx(Typography, { variant: "h5", fontWeight: 600, letterSpacing: 1, fontFamily: fontFamily.workSans, display: { xs: 'none', lg: 'block' }, children: "Request Template List" }), _jsxs(Paper, { component: "form", sx: {
                    p: '2px 4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 400,
                    marginLeft: 'auto',
                }, children: [_jsx(InputBase, { sx: { ml: 1, flex: 1, border: 'none' }, placeholder: "Search here", onChange: e => setSearchValue(e.target.value), inputProps: { 'aria-label': 'search google maps' } }), _jsx(IconButton, { type: "button", sx: { p: '10px' }, "aria-label": "search", children: _jsx(SearchIcon, {}) })] }), _jsx(TableComponent, { columns: columns, data: data, globalFilter: searchValue, setGlobalFilter: setSearchValue })] }));
}
