import {useEffect} from 'react';
import {useQueryClient} from "react-query";
import {useSearchParams} from "react-router-dom";
import { pusherClient } from 'listener/pusher.ts';
import { MessageDto } from 'pages/tickets/types/Conversation.ts';

export const MessageListener = () => {

  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get("conversationId");

  const queryClient = useQueryClient();

  useEffect(() => {
    if(!conversationId) return;
    const channel = pusherClient.subscribe(`conversation-${conversationId}`);
    channel.bind('new-message', (newMessage:MessageDto) => {
      queryClient.setQueryData(['messages', conversationId], (oldMessages:{ messages: MessageDto[] } | undefined) => ({
        ...(oldMessages ?? { messages: [] }),
        messages: [...(oldMessages?.messages ?? []), newMessage],
      }));
    });

    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, [conversationId]);

  return null;
};