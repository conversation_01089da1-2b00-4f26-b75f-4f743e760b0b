import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import Chip from "@mui/material/Chip";
const CustomAccordion = ({ data, onParentDelete, onChildDelete, onChildEdit, onParentEdit }) => {
    const renderAccordion = (item, isChild = false) => {
        const deleteFunction = isChild ? onChildDelete : onParentDelete;
        const editFunction = isChild ? onChildEdit : onParentEdit;
        return (_jsxs(Accordion, { defaultExpanded: false, children: [_jsxs(AccordionSummary, { expandIcon: _jsx(ExpandMoreIcon, {}), "aria-controls": `${item.title.replace(/\s+/g, '-').toLowerCase()}-title`, id: `${item.title.replace(/\s+/g, '-').toLowerCase()}-header`, sx: {
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }, children: [_jsx(Typography, { component: "span", mr: 4, children: item.title }), _jsx(Typography, { fontSize: "12px", component: "span", children: item._id }), _jsxs(Box, { sx: { marginLeft: 'auto' }, children: [item.visibility && (_jsx(Chip, { label: "Visible", color: "primary", sx: {
                                        margin: '2px',
                                        borderRadius: '16px',
                                        padding: '6px 12px',
                                        color: '#333',
                                        fontSize: '14px',
                                        '& .MuiChip-deleteIcon': {
                                            marginLeft: '8px',
                                            color: '#777',
                                        },
                                    } })), item.showInMainMenu && (_jsx(Chip, { label: "Main Menu", color: "success", sx: {
                                        margin: '2px',
                                        borderRadius: '16px',
                                        padding: '6px 12px',
                                        color: '#333',
                                        fontSize: '14px',
                                        '& .MuiChip-deleteIcon': {
                                            marginLeft: '8px',
                                            color: '#777',
                                        },
                                    } })), _jsx(Button, { sx: { padding: 0 }, onClick: (event) => {
                                        event.stopPropagation();
                                        editFunction({ _id: item._id });
                                    }, children: _jsx(IconifyIcon, { icon: 'ion:create-outline', sx: { fontSize: '20px' } }) }), _jsx(Button, { sx: { padding: 0 }, onClick: (event) => {
                                        event.stopPropagation();
                                        deleteFunction({ _id: item._id });
                                    }, children: _jsx(IconifyIcon, { icon: 'ion:trash-outline', sx: { fontSize: '20px' } }) })] })] }), !isChild &&
                    item.children &&
                    item.children.map((child) => renderAccordion(child, true))] }, item.title));
    };
    return (_jsx(Stack, { direction: "column", spacing: 3, my: 5, children: data.map((item) => renderAccordion(item)) }));
};
export default CustomAccordion;
