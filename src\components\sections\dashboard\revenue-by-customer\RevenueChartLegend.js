import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import ButtonBase from '@mui/material/ButtonBase';
const RevenueChartLegend = ({ data, toggleColor, handleLegendToggle }) => {
    let color = '';
    if (toggleColor.currentClients && data.type === 'Current clients') {
        color = 'primary.main';
    }
    else if (toggleColor.subscribers && data.type === 'Subscribers') {
        color = 'secondary.lighter';
    }
    else if (toggleColor.newCustomers && data.type === 'New customers') {
        color = 'secondary.light';
    }
    else {
        color = 'text.secondary';
    }
    return (_jsx(ButtonBase, { onClick: () => handleLegendToggle(data.type), disableRipple: true, children: _jsxs(Stack, { spacing: 0.5, alignItems: "center", children: [_jsx(Box, { height: 8, width: 8, bgcolor: color, borderRadius: 1 }), _jsx(Typography, { variant: "body2", color: "text.secondary", fontFamily: fontFamily.workSans, children: data.type })] }) }));
};
export default RevenueChartLegend;
