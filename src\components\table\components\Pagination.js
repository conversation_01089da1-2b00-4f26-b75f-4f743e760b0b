import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { DOTS, usePagination } from '../hooks/usePagination';
import { IconButton, Tooltip, Box } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
const Pagination = (props) => {
    const { onPageChange, totalCount, siblingCount = 1, currentPage, pageSize, onNextButtonClick, onPreviousButtonClick, } = props;
    const paginationRange = usePagination({
        currentPage,
        totalCount,
        siblingCount,
        pageSize,
    });
    if (currentPage === 0 || (paginationRange && paginationRange.length < 2)) {
        return null;
    }
    const onNext = () => {
        onNextButtonClick();
    };
    const onPrevious = () => {
        onPreviousButtonClick();
    };
    const lastPage = paginationRange && paginationRange[paginationRange.length - 1];
    return (_jsxs(Box, { sx: { display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }, children: [_jsx(Tooltip, { title: "Previous", arrow: true, children: _jsx("span", { children: _jsx(IconButton, { onClick: onPrevious, disabled: currentPage === 1, sx: {
                            backgroundColor: 'transparent',
                            '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            },
                        }, children: _jsx(ChevronLeft, {}) }) }) }), paginationRange &&
                paginationRange.map((pageNumber) => {
                    if (String(pageNumber) === DOTS) {
                        return (_jsx(Box, { sx: {
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 32,
                                height: 32,
                                backgroundColor: 'transparent',
                                color: 'gray',
                                fontSize: '1rem',
                            }, children: "\u2026" }, pageNumber));
                    }
                    return (_jsx(IconButton, { onClick: () => onPageChange(pageNumber - 1), sx: {
                            width: 32,
                            height: 32,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            fontSize: '1rem',
                            backgroundColor: currentPage === pageNumber ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
                            '&:hover': {
                                backgroundColor: currentPage === pageNumber ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                            },
                        }, children: pageNumber }, pageNumber));
                }), _jsx(Tooltip, { title: "Next", arrow: true, children: _jsx("span", { children: _jsx(IconButton, { onClick: onNext, disabled: currentPage === lastPage, sx: {
                            backgroundColor: 'transparent',
                            '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            },
                        }, children: _jsx(ChevronRight, {}) }) }) })] }));
};
export default Pagination;
