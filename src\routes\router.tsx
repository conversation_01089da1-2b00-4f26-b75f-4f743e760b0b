import { Navigate, Route, Routes } from 'react-router-dom';
import { Suspense } from 'react';
import Splash from 'components/loading/Splash.tsx';
import MainLayout from 'layouts/main-layout';
import Dashboard from 'pages/dashboard';
import AuthLayout from 'layouts/auth-layout';
import Login from 'pages/authentication/Login.tsx';
import CategoryAdd from 'pages/category/routes/category-add.tsx';
import CategoryView from 'pages/category/routes/category-view.tsx';
import CategoryUpload from 'pages/category/routes/category-upload.tsx';
import TemplateList from 'pages/templates/routes/template-list.tsx';
import TemplateIgdb from 'pages/templates/routes/template-igdb.tsx';
import TemplateAdd from 'pages/templates/routes/template-add.tsx';
import TemplateRequest from 'pages/templates/routes/template-request.tsx';
import CategoryEdit from 'pages/category/routes/category-edit.tsx';
import TemplateEdit from 'pages/templates/routes/template-edit.tsx';
import AllProducts from 'pages/products/routes/all-products.tsx';
import DeletedProducts from 'pages/products/routes/deleted-products.tsx';
import SpecialProducts from 'pages/products/routes/special-products.tsx';
import EditProduct from 'pages/products/routes/edit-product.tsx';
import ViewProduct from 'pages/products/routes/view-product.tsx';
import HomepageManager from 'pages/homepage-manager/homepage-manager.tsx';
import AllCoupons from 'pages/coupons/routes/all-coupons.tsx';
import CouponEdit from 'pages/coupons/routes/coupon-edit.tsx';
import CouponAdd from 'pages/coupons/routes/coupon-add.tsx';
import AllOrders from 'pages/orders/routes/all-orders.tsx';
import AllBlogs from 'pages/blogs/routes/all-blogs.tsx';
import AddBlog from 'pages/blogs/routes/add-blog.tsx';
import EditBlog from 'pages/blogs/routes/edit-blog.tsx';
import BlogCategories from 'pages/blogs/routes/blog-categories.tsx';
import EditBlogCategory from 'pages/blogs/routes/edit-blog-category.tsx';
import ShopRequest from 'pages/shop/routes/shop-request.tsx';
import FaqView from 'pages/faq/routes/faqView.tsx';
import FaqAdd from 'pages/faq/routes/faqAdd.tsx';
import FaqEdit from 'pages/faq/routes/faqEdit.tsx';
import TicketView from 'pages/tickets/routes/ticketView.tsx';
import TicketDetails from 'pages/tickets/routes/ticketDetails.tsx';
import HelpQuestionView from 'pages/helpQuestions/routes/helpQuestionView.tsx';
import HelpQuestionList from 'pages/helpQuestions/routes/helpQuestionList.tsx';
import SupportChat from 'pages/tickets/components/SupportChat.tsx';
import TaxView from 'pages/tax/routes/taxView.tsx';
import AllGiveAway from 'pages/give-away/routes/all-give-away.tsx';
import GiveawayAdd from 'pages/give-away/routes/give-away-add.tsx';
import GiveAwayEdit from 'pages/give-away/routes/give-away-edit.tsx';
import LevelView from 'pages/levels/routes/levels.tsx';

const routes = [
  {
    path: 'category',
    children: [
      { path: '', element: <CategoryView /> },
      { path: 'add', element: <CategoryAdd /> },
      { path: 'upload', element: <CategoryUpload /> },
      { path: ':id', element: <CategoryEdit /> },
    ],
  },
  {
    path: 'tax-manager',
    children: [{ path: '', element: <TaxView /> }],
  },
  {
    path: 'chat',
    children: [{ path: '', element: <SupportChat /> }],
  },
  {
    path: 'templates',
    children: [
      { path: 'list', element: <TemplateList /> },
      { path: 'igdb', element: <TemplateIgdb /> },
      { path: 'add', element: <TemplateAdd /> },
      { path: ':id', element: <TemplateEdit /> },
      { path: 'request', element: <TemplateRequest /> },
    ],
  },
  {
    path: 'products',
    children: [
      { path: '', element: <AllProducts /> },
      { path: 'special-products', element: <SpecialProducts /> },
      { path: 'deleted-products', element: <DeletedProducts /> },
      { path: 'edit-product/:id', element: <EditProduct /> },
      { path: 'view-product/:id', element: <ViewProduct /> },
    ],
  },
  {
    path: 'coupons',
    children: [
      { path: '', element: <AllCoupons /> },
      { path: 'add', element: <CouponAdd /> },
      { path: ':id', element: <CouponEdit /> },
    ],
  },
  {
    path: 'orders',
    children: [{ path: '', element: <AllOrders /> }],
  },
  {
    path: '/shop-request',
    children: [{ path: '', element: <ShopRequest /> }],
  },
  {
    path: '/faq',
    children: [
      { path: '', element: <FaqView /> },
      { path: 'add', element: <FaqAdd /> },
      { path: 'edit/:id', element: <FaqEdit /> },
    ],
  },
  {
    path: '/tickets',
    children: [
      { path: '', element: <TicketView /> },
      { path: ':id', element: <TicketDetails /> },
    ],
  },
  {
    path: '/helpQuestions',
    children: [
      { path: '', element: <HelpQuestionView /> },
      { path: ':id/subCategory/:subCategoryId', element: <HelpQuestionList /> },
    ],
  },
  {
    path: 'blogs',
    children: [
      { path: '', element: <AllBlogs /> },
      { path: 'add', element: <AddBlog /> },
      { path: 'edit/:id', element: <EditBlog /> },
      { path: 'categories', element: <BlogCategories /> },
      { path: 'edit-category/:id', element: <EditBlogCategory /> },
    ],
  },
  {
    path: 'give-away',
    children: [
      { path: '', element: <AllGiveAway /> },
      { path: 'add', element: <GiveawayAdd /> },
      { path: 'edit/:id', element: <GiveAwayEdit /> },
    ],
  },
  {
    path: 'levels',
    children: [
      { path: '', element: <LevelView /> }
    ],
  },
];

const BasicRouter = () => {
  return (
      <Suspense fallback={<Splash />}>
          <Routes>
              <Route path="auth" element={<AuthLayout />}>
                  <Route path="login" element={<Login />} />
              </Route>

              <Route path="/" element={<MainLayout />}>
                  <Route index element={<Dashboard />} />
                  <Route path="homepage-manager" element={<HomepageManager />} />

                  {routes.map(({ path, children }) =>
                      children.map(({ path: childPath, element }) => (
                          <Route key={`${path}/${childPath}`} path={`${path}/${childPath}`} element={element} />
                      ))
                  )}
              </Route>

              <Route path="/404" element={<p>Not Found</p>} />
              <Route path="*" element={<Navigate to="/404" />} />
          </Routes>
      </Suspense>

  );
};

export default BasicRouter;
