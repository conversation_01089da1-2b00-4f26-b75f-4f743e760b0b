import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import {
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import Paper from '@mui/material/Paper';
import { uploadFormat } from 'pages/category/const/uploadFormat.ts';
import Typography from '@mui/material/Typography';
import IconifyIcon from 'components/base/IconifyIcon.tsx';

interface BasicModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
}

export default function BasicModal({ open, setOpen } : BasicModalProps) {

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogActions>
          <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
        </DialogActions>
        <DialogTitle id="alert-dialog-title">Bulk Category Upload</DialogTitle>
        <DialogContent sx={{ padding: 0 }}>
          <TableContainer component={Paper}>
            <Table sx={{ width: '100%' }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell sx={{
                    width: {
                      xs: '20%',
                      sm: '50%',
                    }
                  }}>Field</TableCell>
                  <TableCell sx={{
                    width: {
                      xs: '20%',
                      sm: '50%',
                    }
                  }}>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {uploadFormat.map((row) => (
                  <TableRow
                    key={row.field}
                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                  >
                    <TableCell component="th" scope="row">
                      {row.field}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        <strong>Type:</strong> {row.type}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Required:</strong> {row.required}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Example:</strong> {row.example}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
}