import { jsx as _jsx } from "react/jsx-runtime";
import CheckBoxBlankIcon from 'components/icons/CheckBoxBlankIcon';
import CheckBoxCheckedIcon from 'components/icons/CheckBoxCheckedIcon';
import CheckBoxIndeterminateIcon from 'components/icons/CheckBoxIndeterminateIcon';
const Checkbox = {
    defaultProps: {
        icon: _jsx(CheckBoxBlankIcon, {}),
        checkedIcon: _jsx(CheckBoxCheckedIcon, {}),
        indeterminateIcon: _jsx(CheckBoxIndeterminateIcon, {}),
    },
    styleOverrides: {
        root: ({ theme }) => ({
            color: theme.palette.text.secondary,
        }),
        sizeMedium: ({ theme }) => ({
            '& .MuiSvgIcon-root': {
                fontSize: theme.typography.button.fontSize,
            },
        }),
        sizeSmall: ({ theme }) => ({
            '& .MuiSvgIcon-root': {
                fontSize: theme.typography.caption.fontSize,
            },
        }),
    },
};
export default Checkbox;
