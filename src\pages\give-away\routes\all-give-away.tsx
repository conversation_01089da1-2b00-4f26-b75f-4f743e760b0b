import { useNavigate } from 'react-router-dom';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import Button from '@mui/material/Button';
import useDeleteGiveAway from 'pages/give-away/hooks/useDeleteGiveAway.ts';
import useGiveAway from 'pages/give-away/hooks/useGiveAway.ts';
import { GiveawayDTO } from 'pages/give-away/types/give-away.ts';
import { formatDate } from 'functions/time.ts';

export default function AllGiveAway() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [itemToDelete, setItemToDelete] = useState({ _id: '' });
  const { giveAway, giveAwayLoading } = useGiveAway();
  const { giveAwayDelete } = useDeleteGiveAway(itemToDelete);

  useEffect(() => {
    if (!itemToDelete._id) return;

    giveAwayDelete().finally(() => setItemToDelete({ _id: '' }));
  }, [itemToDelete]);

  const columns = useMemo<ColumnDef<GiveawayDTO>[]>(
    () => [
      {
        header: 'Title',
        accessorKey: 'title',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Start Date',
        accessorKey: 'startDate',
        cell: (info) =>
          formatDate(info.getValue() as string),
        enableGlobalFilter: false,
      },
      {
        header: 'End Date',
        accessorKey: 'endDate',
        cell: (info) =>
          formatDate(info.getValue() as string),
        enableGlobalFilter: false,
      },
      {
        header: 'Prizes',
        accessorKey: 'prizes',
        cell: (info) =>
          (info.getValue() as string[]).join(', '),
        enableGlobalFilter: true,
      },
      {
        header: 'Actions Count',
        accessorFn: (row) => row.actions?.length ?? 0,
        id: 'actionsCount',
        cell: (info) => info.getValue(),
        enableGlobalFilter: false,
      },
      {
        header: 'Actions',
        id: 'actionsMenu',
        cell: (info) => {
          const id = info.row.original._id;
          const options = [
            {
              title: 'Edit',
              action: () => navigate(`/give-away/edit/${id}`),
            },
            {
              title: 'Delete',
              action: () => setItemToDelete({ _id: id! }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (giveAwayLoading || !giveAway) return <CircularProgress />;

  return (
    <>
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Give Away
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Button type="submit" variant="contained" size="small" onClick={() => navigate(`/give-away/add`)}>
          Add Give Away
        </Button>
        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={giveAway}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}