import * as React from 'react';
import { Dispatch, SetStateAction } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import {ButtonGroup, DialogActions, FormHelperText, InputLabel} from '@mui/material';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import useBannerForm from 'pages/homepage-manager/hook/useBannerForm.ts';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import SingleFileInput from 'components/single-file-input.tsx';
import SelectMenu from 'components/select-menu.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import { KeyValues } from 'pages/homepage-manager/types/homepage-manager.ts';
import useBannerDetails from 'pages/homepage-manager/hook/useBannerDetails.ts';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import {categoryOptions} from "pages/homepage-manager/const/category-options.ts";
import Typography from "@mui/material/Typography";

interface BasicModalProps {
  open: KeyValues;
  setOpen: Dispatch<SetStateAction<KeyValues>>;
}

export default function BannerModal({ open, setOpen } : BasicModalProps) {

    const {bannerDetails, bannerDetailsLoading} = useBannerDetails({_id: open._id})
    const {formik} = useBannerForm({setOpen, bannerDetails, _id: open._id});

    const handleClose = () => {
        setOpen({_id: undefined, open: false});
    };

    return (
        <React.Fragment>
            <Dialog
                open={open.open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                fullWidth={true}
            >
                <Stack direction="row" spacing={2} justifyContent="space-between">
                    <DialogTitle id="alert-dialog-title" sx={{ padding: '10px 0' }}>
                        {open._id ? 'Edit' : 'Add'} Banner
                    </DialogTitle>
                    <DialogActions>
                        <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
                    </DialogActions>
                </Stack>
                <form onSubmit={formik.handleSubmit}>
                    <DialogContent sx={{ padding: 0 }}>
                        {(bannerDetailsLoading && open.open) && <CircularProgress />}
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="order"
                            >
                                Order
                            </InputLabel>
                            <ButtonGroup variant="outlined" color="primary">
                                <Button onClick={()=> formik.setFieldValue("order", formik.values.order - 1)} disabled={false}>-</Button>
                                <Typography
                                    sx={{ minWidth: 40, display: "flex", alignItems: "center", justifyContent: "center" }}
                                >
                                    {formik.values.order}
                                </Typography>
                                <Button onClick={()=> formik.setFieldValue("order", formik.values.order + 1)}>+</Button>
                            </ButtonGroup>
                        </Box>
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="link"
                            >
                                Link
                            </InputLabel>
                            <TextField
                                id="link"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("link")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>
                        {formik.values.order <=2 && <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="tag"
                            >
                                Tag
                            </InputLabel>
                            <TextField
                                id="link"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("tag")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>}
                        {formik.values.order !== 3 && <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="title"
                            >
                                Title
                            </InputLabel>
                            <TextField
                                id="link"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("title")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>}
                        {formik.values.order <= 2 && <Box>
                            <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="shortSummary"
                    >
                        Short Summary
                    </InputLabel>
                    <TextField
                        id="shortSummary"
                        type="string"
                        fullWidth
                        required={true}
                        {...formik.getFieldProps("shortSummary")}
                        sx={{ marginBottom: 2 }}
                    />
                </Box>}
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="discountPercent"
                            >
                                Discount Percent
                            </InputLabel>
                            <TextField
                                id="discountPercent"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("discountPercent")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="couponCode"
                            >
                                Coupon Code
                            </InputLabel>
                            <TextField
                                id="discountPercent"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("couponCode")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="existingPrice"
                            >
                                Price
                            </InputLabel>
                            <TextField
                                id="discountPercent"
                                type="string"
                                fullWidth
                                required={true}
                                {...formik.getFieldProps("existingPrice")}
                                sx={{ marginBottom: 2 }}
                            />
                        </Box>
                        <Box>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="category"
                            >
                                Category
                            </InputLabel>
                            <SelectMenu
                                id="category"
                                value={formik.values.category}
                                handleChange={(id, value)=> formik.setFieldValue(id, value)}
                                options={categoryOptions}
                            />
                        </Box>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <Box>
                                    <InputLabel
                                        component="label"
                                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                                        size="small"
                                        htmlFor="startTime"
                                    >
                                        Start Time
                                        <DateTimePicker
                                            sx={{
                                                display: "block",
                                                "& .MuiInputBase-root": {
                                                    width: "100%",
                                                },
                                                "& fieldset": {
                                                    border: "none",
                                                },
                                            }}
                                            label={null}
                                            value={dayjs(formik.values.startTime)}
                                            onChange={(newValue) => formik.setFieldValue("startTime", newValue)}
                                        />
                                    </InputLabel>
                                </Box>
                                <Box>
                                    <InputLabel
                                        component="label"
                                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                                        size="small"
                                        htmlFor="endTime"
                                    >
                                        End Time
                                        <DateTimePicker
                                            sx={{
                                                display: "block",
                                                "& .MuiInputBase-root": {
                                                    width: "100%",
                                                },
                                            }}
                                            label={null}
                                            value={dayjs(formik.values.endTime)}
                                            onChange={(newValue) => formik.setFieldValue("endTime", newValue)}
                                        />
                                    </InputLabel>
                                </Box>
                        </LocalizationProvider>
                        <Box sx={{ marginTop: '20px', display:"flex", justifyContent:"space-evenly"}}>
                            {formik.isSubmitting ? <CircularProgress /> : <SingleFileInput existingFile={open._id && bannerDetails?.images.desktop} getFile={(file)=> formik.setFieldValue("images.desktop", file)} label="Desktop Image" />}
                            {formik.values.order <= 2 && <>
                                {formik.isSubmitting ? <CircularProgress /> : <SingleFileInput existingFile={open._id && bannerDetails?.logoImage} getFile={(file)=> formik.setFieldValue("logoImage", file)} label="Additional Logo Image" />}
                            </>}
                        </Box>
                        <Box>
                            {formik.errors.images?.desktop && <FormHelperText error>{formik.errors.images?.desktop}</FormHelperText>}
                        </Box>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleClose} variant="outlined" type="button">
                            Cancel
                        </Button>
                        <Button type="submit" variant="contained" disabled={formik.isSubmitting}>
                            {formik.isSubmitting ? 'Saving...' : 'Save'}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </React.Fragment>
    );
}