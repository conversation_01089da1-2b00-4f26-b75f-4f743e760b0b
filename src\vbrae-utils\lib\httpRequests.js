import { fetchExtra } from 'vbrae-utils';
export async function postRequest({ url, data, useAuth = false, otherHeaders = {}, }) {
    return (await fetchExtra(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...otherHeaders,
        },
        body: JSON.stringify({ ...data }),
    }, useAuth));
}
export async function patchRequest({ url, data, otherHeaders = {}, useAuth = false, }) {
    return await fetchExtra(url, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            ...otherHeaders,
        },
        body: JSON.stringify({
            ...data,
        }),
    }, useAuth);
}
export async function getRequest({ url, useAuth = false, otherHeaders = {}, }) {
    return (await fetchExtra(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            ...otherHeaders,
        },
    }, useAuth)).response;
}
export async function deleteRequest({ url, useAuth = false, otherHeaders = {}, }) {
    return (await fetchExtra(url, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            ...otherHeaders,
        },
    }, useAuth)).response;
}
export async function postImageRequest({ url, data, useAuth = false, otherHeaders = {}, }) {
    return (await fetchExtra(url, {
        method: 'POST',
        headers: {
            ...otherHeaders,
        },
        body: data,
    }, useAuth));
}
