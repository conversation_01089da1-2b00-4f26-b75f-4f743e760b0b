import Typography from '@mui/material/Typography';
import { Card, CardActions, CardContent, Switch } from '@mui/material';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import useSubCategoryQuestions from 'pages/helpQuestions/hooks/useSubCategoryQuestions.ts';
import { useParams } from 'react-router-dom';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import QuestionModal from 'components/modals/QuestionModal.tsx';
import {useEffect, useState} from 'react';
import { AccordionQuestionDto } from 'pages/category/types/accordion.ts';
import useDeleteQuestion from "pages/helpQuestions/hooks/useDeleteQuestion.ts";
import QuestionSubCategoryModal from "components/modals/QuestionSubcategoryModal.tsx";
import IconButton from "@mui/material/IconButton";
import CreateIcon from '@mui/icons-material/Create';
import DeleteIcon from '@mui/icons-material/Delete';
import useDeleteSubCategory from "pages/helpQuestions/hooks/useDeleteSubCategory.ts";

export default function HelpQuestionList() {
  const { id='', subCategoryId='' } = useParams();

  const [open, setOpen] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [categoryToUpdate, setCategoryToUpdate] = useState({
    title: '',
    isActive: false,
    _id: '',
  });

  const [questionToUpdate, setQuestionToUpdate] = useState({
    title: '',
    content: '',
    isActive: false,
    _id: '',
  });
  const [questionToDelete, setQuestionToDelete] = useState("")

  const { data, loading } = useSubCategoryQuestions({ id, subCategoryId });
  const {questionDelete} = useDeleteQuestion({questionId: questionToDelete, categoryId: id, subcategoryId: subCategoryId})
  const {subCategoryDelete} = useDeleteSubCategory({ categoryId: id, subcategoryId: subCategoryId})

  const handleEdit = (question: AccordionQuestionDto) => {
    setQuestionToUpdate({
      title: question.title,
      content: question.content,
      isActive: question.isActive,
      _id: question._id,
    });

    setOpen(true)
  };

  useEffect(() => {
    if(!questionToDelete) return;

    questionDelete().finally(() => setQuestionToDelete(""));
  }, [questionToDelete]);

  if (!data || loading) return <CircularProgress />;

  function handleUpdateCategory(title: string) {
    setCategoryToUpdate({
      _id: subCategoryId,
      isActive: false,
      title
    })

    setModalOpen(true)
  }

  return (
    <Paper
      sx={{
        p: '40px',
        display: 'flex',
        flexDirection: 'column',
        width: { xs: '100%' },
      }}
    >
      <Stack spacing={2} alignItems="center" justifyContent="space-between" mb={4}>
        <Typography
          variant="h5"
          fontWeight={600}
          sx={{ fontSize: { xs: '16px', sm: '18px' }, paddingTop: '20px' }}
        >
          {data.subcategoryTitle}
          <IconButton onClick={() => handleUpdateCategory(data.subcategoryTitle)} sx={{ color: "white", p: 0, marginLeft: 2 ,border: "1px solid white" }}>
            <CreateIcon />
          </IconButton>

          <IconButton onClick={()=> subCategoryDelete()} sx={{ color: "white", p: 0, marginLeft: 2 ,border: "1px solid white" }}>
            <DeleteIcon />
          </IconButton>
        </Typography>
        <Button variant="contained" color="primary" onClick={()=> {
          setOpen(true);
          setQuestionToUpdate({
            title: '',
            content: '',
            isActive: false,
            _id: '',
          })
        }}>
          Add Question
        </Button>
      </Stack>
      {modalOpen && <QuestionSubCategoryModal
        open={modalOpen}
        handleClose={()=> setModalOpen(false)}
        state={categoryToUpdate}
      />}
      {open && <QuestionModal
          open={open}
          state={questionToUpdate}
          handleClose={() => setOpen(false)}
      />}
      {data.questions.map((question) => (
        <Card key={question._id} sx={{ marginBottom: 2, padding: 2 }}>
          <CardContent>
            <Typography variant="h4">Question: {question.title}</Typography>

            <Typography mt={2}>Answer : {question.content}</Typography>

            <Typography mt={2} variant="body2" color="text.secondary">
              {question.isActive ? 'Active' : 'Inactive'}
            </Typography>
            <Switch color="primary" disabled={true} checked={question.isActive} />
          </CardContent>
          <CardActions>
            <Button variant="contained" color="primary" onClick={()=> handleEdit(question)}>
              Edit
            </Button>
            <Button variant="contained" color="error" onClick={()=> setQuestionToDelete(question._id)}>
              Delete
            </Button>
          </CardActions>
        </Card>
      ))}
    </Paper>
  );
}